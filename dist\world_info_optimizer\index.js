var __webpack_modules__ = {
  "./src/world_info_optimizer/api.ts": 
  /*!*****************************************!*\
  !*** ./src/world_info_optimizer/api.ts ***!
  \*****************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TavernAPI: () => (/* binding */ TavernAPI)\n/* harmony export */ });\n/* harmony import */ var _ui_modals__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ui/modals */ \"./src/world_info_optimizer/ui/modals.ts\");\n// src/world_info_optimizer/api.ts\n\n/**\n * 错误处理装饰器或高阶函数，用于封装API调用。\n * @param fn 要封装的异步函数\n * @param context 错误日志的上下文\n */\nconst errorCatched = (fn, context = 'TavernAPI') => {\n    return async (...args) => {\n        try {\n            return await fn(...args);\n        }\n        catch (error) {\n            // 用户取消操作（例如在模态框中点击取消）通常会抛出null或undefined\n            if (error) {\n                console.error(`[${context}] Error:`, error);\n                // 可以在这里决定是否显示一个全局的错误提示\n                await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_0__.showModal)({\n                    type: 'alert',\n                    title: 'API调用异常',\n                    text: `操作中发生未知错误，请检查开发者控制台获取详细信息。`,\n                });\n            }\n            return null;\n        }\n    };\n};\n/**\n * 获取全局TavernHelper实例。\n * @returns {TavernHelper}\n * @throws 如果TavernHelper未定义，则抛出错误。\n */\nconst getTavernHelper = () => {\n    const parentWin = window.parent;\n    if (!parentWin.TavernHelper) {\n        throw new Error('TavernHelper is not available on the parent window.');\n    }\n    return parentWin.TavernHelper;\n};\nconst getSillyTavernContext = () => {\n    const parentWin = window.parent;\n    if (!parentWin.SillyTavern || typeof parentWin.SillyTavern.getContext !== 'function') {\n        // 在这种情况下，我们不应该抛出错误，而应该返回一个默认的、无害的上下文。\n        // 因为脚本可能在SillyTavern完全加载之前运行。\n        console.warn('[WIO API] SillyTavern.getContext is not available.');\n        return {\n            characters: [],\n            characterId: null,\n            chatId: null,\n        };\n    }\n    return parentWin.SillyTavern.getContext();\n};\n// API封装模块\nclass TavernAPIWrapper {\n    helper;\n    constructor() {\n        this.helper = getTavernHelper();\n    }\n    // Lorebook APIs\n    createLorebook = errorCatched(async (name) => this.helper.createLorebook(name));\n    deleteLorebook = errorCatched(async (name) => this.helper.deleteLorebook(name));\n    getLorebooks = errorCatched(async () => this.helper.getLorebooks());\n    getLorebookSettings = errorCatched(async () => this.helper.getLorebookSettings());\n    setLorebookSettings = errorCatched(async (settings) => this.helper.setLorebookSettings(settings));\n    // Lorebook Entry APIs\n    getLorebookEntries = errorCatched(async (name) => this.helper.getLorebookEntries(name));\n    setLorebookEntries = errorCatched(async (name, entries) => this.helper.setLorebookEntries(name, entries));\n    createLorebookEntries = errorCatched(async (name, entries) => this.helper.createLorebookEntries(name, entries));\n    deleteLorebookEntries = errorCatched(async (name, uids) => this.helper.deleteLorebookEntries(name, uids));\n    // Character-specific Lorebook APIs\n    getCharLorebooks = errorCatched(async (charData) => this.helper.getCharLorebooks(charData));\n    getCurrentCharLorebooks = errorCatched(async () => this.helper.getCharLorebooks());\n    setCurrentCharLorebooks = errorCatched(async (lorebooks) => this.helper.setCurrentCharLorebooks(lorebooks));\n    // Chat Lorebook APIs\n    getChatLorebook = errorCatched(async () => this.helper.getChatLorebook());\n    setChatLorebook = errorCatched(async (name) => this.helper.setChatLorebook(name));\n    getOrCreateChatLorebook = errorCatched(async (name) => this.helper.getOrCreateChatLorebook(name));\n    // Regex APIs\n    getRegexes = errorCatched(async () => this.helper.getTavernRegexes({ scope: 'all' }));\n    replaceRegexes = errorCatched(async (regexes) => this.helper.replaceTavernRegexes(regexes, { scope: 'all' }));\n    // Character Data APIs\n    getCharData = errorCatched(async () => this.helper.getCharData());\n    // Misc APIs\n    saveSettings = errorCatched(async () => this.helper.builtin.saveSettings());\n    // Context API\n    getContext = () => getSillyTavernContext();\n    // Direct access to specific properties if needed\n    get Character() {\n        return this.helper.Character;\n    }\n}\n// 导出单例\nconst TavernAPI = new TavernAPIWrapper();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/api.ts\n\n}");
  },
  "./src/world_info_optimizer/constants.ts": 
  /*!***********************************************!*\
  !*** ./src/world_info_optimizer/constants.ts ***!
  \***********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   BUTTON_ICON_URL: () => (/* binding */ BUTTON_ICON_URL),\n/* harmony export */   BUTTON_ID: () => (/* binding */ BUTTON_ID),\n/* harmony export */   BUTTON_TEXT_IN_MENU: () => (/* binding */ BUTTON_TEXT_IN_MENU),\n/* harmony export */   BUTTON_TOOLTIP: () => (/* binding */ BUTTON_TOOLTIP),\n/* harmony export */   COLLAPSE_ALL_BTN_ID: () => (/* binding */ COLLAPSE_ALL_BTN_ID),\n/* harmony export */   COLLAPSE_CURRENT_BTN_ID: () => (/* binding */ COLLAPSE_CURRENT_BTN_ID),\n/* harmony export */   CREATE_LOREBOOK_BTN_ID: () => (/* binding */ CREATE_LOREBOOK_BTN_ID),\n/* harmony export */   INITIAL_ACTIVE_TAB: () => (/* binding */ INITIAL_ACTIVE_TAB),\n/* harmony export */   LOREBOOK_INSERTION_POSITIONS: () => (/* binding */ LOREBOOK_INSERTION_POSITIONS),\n/* harmony export */   LOREBOOK_LOGIC_OPTIONS: () => (/* binding */ LOREBOOK_LOGIC_OPTIONS),\n/* harmony export */   PANEL_ID: () => (/* binding */ PANEL_ID),\n/* harmony export */   REFRESH_BTN_ID: () => (/* binding */ REFRESH_BTN_ID),\n/* harmony export */   SEARCH_INPUT_ID: () => (/* binding */ SEARCH_INPUT_ID)\n/* harmony export */ });\n// src/world_info_optimizer/constants.ts\n// --- UI Element IDs ---\nconst PANEL_ID = 'world-info-optimizer-panel';\nconst BUTTON_ID = 'world-info-optimizer-button';\nconst SEARCH_INPUT_ID = 'wio-search-input';\nconst REFRESH_BTN_ID = 'wio-refresh-btn';\nconst COLLAPSE_CURRENT_BTN_ID = 'wio-collapse-current-btn';\nconst COLLAPSE_ALL_BTN_ID = 'wio-collapse-all-btn';\nconst CREATE_LOREBOOK_BTN_ID = 'wio-create-lorebook-btn';\n// --- UI Display Text & URLs ---\nconst BUTTON_ICON_URL = 'https://i.postimg.cc/bY23wb9Y/IMG-20250626-000247.png';\nconst BUTTON_TOOLTIP = '世界书 & 正则便捷管理 (WIO)';\nconst BUTTON_TEXT_IN_MENU = '世界书 & 正则便捷管理 (WIO)';\n// --- Lorebook Configuration Options ---\nconst LOREBOOK_INSERTION_POSITIONS = {\n    before_character_definition: '角色定义前',\n    after_character_definition: '角色定义后',\n    before_example_messages: '聊天示例前',\n    after_example_messages: '聊天示例后',\n    before_author_note: '作者笔记前',\n    after_author_note: '作者笔记后',\n    at_depth_as_system: '@D ⚙ 系统',\n    at_depth_as_assistant: '@D 🗨️ 角色',\n    at_depth_as_user: '@D 👤 用户',\n};\nconst LOREBOOK_LOGIC_OPTIONS = {\n    and_any: '任一 AND',\n    and_all: '所有 AND',\n    not_any: '任一 NOT',\n    not_all: '所有 NOT',\n};\n// --- Other Constants ---\nconst INITIAL_ACTIVE_TAB = 'global-lore';\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/constants.ts\n\n}");
  },
  "./src/world_info_optimizer/core.ts": 
  /*!******************************************!*\
  !*** ./src/world_info_optimizer/core.ts ***!
  \******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createLorebook: () => (/* binding */ createLorebook),\n/* harmony export */   createLorebookEntry: () => (/* binding */ createLorebookEntry),\n/* harmony export */   createRegex: () => (/* binding */ createRegex),\n/* harmony export */   deleteLorebook: () => (/* binding */ deleteLorebook),\n/* harmony export */   deleteLorebookEntry: () => (/* binding */ deleteLorebookEntry),\n/* harmony export */   deleteRegex: () => (/* binding */ deleteRegex),\n/* harmony export */   loadAllData: () => (/* binding */ loadAllData),\n/* harmony export */   renameLorebook: () => (/* binding */ renameLorebook),\n/* harmony export */   setGlobalLorebookEnabled: () => (/* binding */ setGlobalLorebookEnabled),\n/* harmony export */   updateLorebookEntry: () => (/* binding */ updateLorebookEntry),\n/* harmony export */   updateRegex: () => (/* binding */ updateRegex)\n/* harmony export */ });\n/* harmony import */ var _api__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./api */ \"./src/world_info_optimizer/api.ts\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./store */ \"./src/world_info_optimizer/store.ts\");\n// src/world_info_optimizer/core.ts\n\n\n/**\n * 加载所有初始数据。\n * 这是应用启动时调用的主要数据获取函数。\n */\nconst loadAllData = async () => {\n    const currentState = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    // 防止并发加载\n    if (currentState.isLoading) {\n        console.log('[WIO Core] Data loading already in progress, skipping...');\n        return;\n    }\n    // 开始加载\n    (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({\n        ...s,\n        isDataLoaded: false,\n        isLoading: true,\n        loadError: null,\n    }));\n    try {\n        const context = _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getContext();\n        const { characters: allCharacters, characterId, chatId } = context;\n        const hasActiveCharacter = characterId !== undefined && characterId !== null;\n        const hasActiveChat = chatId !== undefined && chatId !== null;\n        // --- 并行获取基础数据 ---\n        const results = await Promise.allSettled([\n            _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getRegexes(),\n            _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookSettings(),\n            _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebooks(),\n            hasActiveCharacter ? _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getCharData() : Promise.resolve(null),\n            hasActiveCharacter ? _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getCurrentCharLorebooks() : Promise.resolve(null),\n            hasActiveChat ? _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getChatLorebook() : Promise.resolve(null),\n        ]);\n        // --- 安全地从Promise结果中提取数据 ---\n        const allUIRegexes = results[0].status === 'fulfilled' ? results[0].value : [];\n        const globalSettings = results[1].status === 'fulfilled' ? results[1].value : {};\n        const allBookFileNames = results[2].status === 'fulfilled' ? results[2].value : [];\n        const charData = results[3].status === 'fulfilled' ? results[3].value : null;\n        const charLinkedBooks = results[4].status === 'fulfilled' ? results[4].value : null;\n        const chatLorebook = results[5].status === 'fulfilled' ? results[5].value : null;\n        // --- 数据处理 ---\n        // 1. 正则表达式处理\n        const globalRegexes = (allUIRegexes || []).filter(r => r.scope === 'global');\n        const characterRegexes = processCharacterRegexes(allUIRegexes, charData);\n        // 2. 世界书文件和启用状态\n        const enabledGlobalBooks = new Set(globalSettings?.selected_global_lorebooks || []);\n        const allLorebooks = (allBookFileNames || []).map(name => ({\n            name: name,\n            enabled: enabledGlobalBooks.has(name),\n        }));\n        // 3. 当前角色关联的世界书\n        const charBookSet = new Set();\n        if (charLinkedBooks?.primary)\n            charBookSet.add(charLinkedBooks.primary);\n        if (charLinkedBooks?.additional)\n            charLinkedBooks.additional.forEach(b => charBookSet.add(b));\n        const characterLorebooks = Array.from(charBookSet);\n        // 4. 计算所有角色对世界书的使用情况 (lorebookUsage)\n        const lorebookUsage = new Map();\n        const knownBookNames = new Set(allBookFileNames || []);\n        if (Array.isArray(allCharacters)) {\n            for (const char of allCharacters) {\n                if (!char?.name)\n                    continue;\n                // 注意: getCharLorebooks 是同步还是异步取决于TavernHelper的实现\n                // 为保险起见，我们假设它可能返回一个Promise\n                const books = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getCharLorebooks({ name: char.name });\n                const charBooks = new Set();\n                if (books?.primary)\n                    charBooks.add(books.primary);\n                if (books?.additional)\n                    books.additional.forEach(b => charBooks.add(b));\n                charBooks.forEach(bookName => {\n                    if (!lorebookUsage.has(bookName)) {\n                        lorebookUsage.set(bookName, []);\n                    }\n                    lorebookUsage.get(bookName)?.push(char.name);\n                    knownBookNames.add(bookName); // 确保所有被使用的书都被加载\n                });\n            }\n        }\n        // 5. 汇总所有需要加载条目的世界书\n        characterLorebooks.forEach(b => knownBookNames.add(b));\n        if (chatLorebook) {\n            knownBookNames.add(chatLorebook);\n        }\n        // 6. 并行加载所有世界书的条目\n        const lorebookEntries = new Map();\n        const entryPromises = Array.from(knownBookNames).map(async (name) => {\n            const entries = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookEntries(name);\n            if (entries) {\n                lorebookEntries.set(name, entries);\n            }\n        });\n        await Promise.all(entryPromises);\n        // 7. 更新全局状态\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.setAllData)({\n            globalRegexes,\n            characterRegexes,\n            allLorebooks,\n            characterLorebooks,\n            chatLorebook: chatLorebook || null,\n            lorebookEntries,\n            lorebookUsage,\n        });\n    }\n    catch (error) {\n        console.error('[WIO Core] Failed to load all data:', error);\n        const errorMessage = error instanceof Error ? error.message : '未知错误';\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({\n            ...s,\n            isDataLoaded: false, // 确保清除旧数据\n            isLoading: false,\n            loadError: `数据加载失败: ${errorMessage}`,\n        }));\n    }\n    finally {\n        // 确保加载状态总是被重置\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, isLoading: false }));\n    }\n};\n/**\n * 处理和合并来自UI和角色卡中的角色特定正则表达式。\n * @param allUIRegexes 从Tavern API获取的所有UI正则\n * @param charData 当前角色的数据\n * @returns 合并和去重后的角色正则表达式数组\n */\nfunction processCharacterRegexes(allUIRegexes, charData) {\n    const characterUIRegexes = allUIRegexes?.filter(r => r.scope === 'character') || [];\n    let cardRegexes = [];\n    if (charData && _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.Character) {\n        try {\n            const character = new _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.Character(charData);\n            cardRegexes = (character.getRegexScripts() || []).map((r, i) => ({\n                id: r.id || `card-${Date.now()}-${i}`,\n                script_name: r.scriptName || '未命名卡内正则',\n                find_regex: r.findRegex,\n                replace_string: r.replaceString,\n                enabled: !r.disabled,\n                scope: 'character',\n                source: 'card',\n            }));\n        }\n        catch (e) {\n            console.warn(\"[WIO Core] Couldn't parse character card regex scripts:\", e);\n        }\n    }\n    // 去重逻辑：UI中的正则优先\n    const uiRegexIdentifiers = new Set(characterUIRegexes.map(r => `${r.script_name}::${r.find_regex}::${r.replace_string}`));\n    const uniqueCardRegexes = cardRegexes.filter(r => {\n        const identifier = `${r.script_name}::${r.find_regex}::${r.replace_string}`;\n        return !uiRegexIdentifiers.has(identifier);\n    });\n    return [...characterUIRegexes, ...uniqueCardRegexes];\n}\n// --- Lorebook CRUD Operations ---\n/**\n * 创建一个新的空世界书。\n * @param bookName 新世界书的名称\n */\nconst createLorebook = async (bookName) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.createLorebook(bookName);\n    if (result !== null) {\n        await loadAllData(); // 重新加载所有数据以确保状态同步\n    }\n};\n/**\n * 重命名一个世界书。\n * @param oldName 旧名称\n * @param newName 新名称\n */\nconst renameLorebook = async (oldName, newName) => {\n    // 1. 获取旧书的所有条目\n    const entries = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookEntries(oldName);\n    if (entries === null) {\n        throw new Error(`Failed to get entries for lorebook: ${oldName}`);\n    }\n    // 2. 创建一个新书\n    const createResult = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.createLorebook(newName);\n    if (createResult === null) {\n        throw new Error(`Failed to create new lorebook: ${newName}`);\n    }\n    // 3. 将条目复制到新书\n    if (entries.length > 0) {\n        const setResult = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(newName, entries);\n        if (setResult === null) {\n            // 清理：如果条目设置失败，删除新建的书\n            await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(newName);\n            throw new Error(`Failed to set entries for new lorebook: ${newName}`);\n        }\n    }\n    // 4. 删除旧书\n    const deleteResult = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(oldName);\n    if (deleteResult === null) {\n        // 这不是一个关键错误，但应该记录下来\n        console.warn(`Failed to delete old lorebook \"${oldName}\" after renaming.`);\n    }\n    // 5. 刷新数据\n    await loadAllData();\n};\n/**\n * 删除一个世界书。\n * @param bookName 要删除的世界书的名称\n */\nconst deleteLorebook = async (bookName) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebook(bookName);\n    if (result !== null) {\n        await loadAllData();\n    }\n};\n// --- Lorebook Entry CRUD Operations ---\n/**\n * 在指定的世界书中创建一个新条目。\n * @param bookName 世界书名称\n * @param entryData 要创建的条目的数据\n */\nconst createLorebookEntry = async (bookName, entryData) => {\n    // Tavern API需要一个数组\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.createLorebookEntries(bookName, [entryData]);\n    if (result !== null && result.length > 0) {\n        const newState = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n        const newEntry = result[0]; // API会返回创建后的条目，包含UID\n        const currentEntries = newState.lorebookEntries.get(bookName) || [];\n        newState.lorebookEntries.set(bookName, [...currentEntries, newEntry]);\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(newState.lorebookEntries) }));\n    }\n};\n/**\n * 更新世界书中的一个现有条目。\n * @param bookName 世界书名称\n * @param uid 要更新的条目的唯一ID\n * @param updates 要应用于条目的部分更新\n */\nconst updateLorebookEntry = async (bookName, uid, updates) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const entries = state.lorebookEntries.get(bookName);\n    if (!entries) {\n        throw new Error(`[WIO Core] Book not found in state: ${bookName}`);\n    }\n    let entryUpdated = false;\n    const updatedEntries = entries.map(entry => {\n        if (entry.uid === uid) {\n            entryUpdated = true;\n            return { ...entry, ...updates };\n        }\n        return entry;\n    });\n    if (entryUpdated) {\n        const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookEntries(bookName, updatedEntries);\n        if (result !== null) {\n            state.lorebookEntries.set(bookName, updatedEntries);\n            (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));\n        }\n    }\n};\n/**\n * 从世界书中删除一个条目。\n * @param bookName 世界书名称\n * @param uid 要删除的条目的唯一ID\n */\nconst deleteLorebookEntry = async (bookName, uid) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.deleteLorebookEntries(bookName, [uid]);\n    if (result !== null) {\n        const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n        const entries = state.lorebookEntries.get(bookName) || [];\n        const filteredEntries = entries.filter(entry => entry.uid !== uid);\n        state.lorebookEntries.set(bookName, filteredEntries);\n        (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, lorebookEntries: new Map(state.lorebookEntries) }));\n    }\n};\n// --- Global Settings ---\n/**\n * 设置一个全局世界书的启用状态。\n * @param bookName 世界书的名称\n * @param enabled true为启用, false为禁用\n */\nconst setGlobalLorebookEnabled = async (bookName, enabled) => {\n    const settings = (await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.getLorebookSettings()) || {};\n    if (!settings.selected_global_lorebooks) {\n        settings.selected_global_lorebooks = [];\n    }\n    const enabledBooks = new Set(settings.selected_global_lorebooks);\n    if (enabled) {\n        enabledBooks.add(bookName);\n    }\n    else {\n        enabledBooks.delete(bookName);\n    }\n    settings.selected_global_lorebooks = Array.from(enabledBooks);\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.setLorebookSettings(settings);\n    if (result !== null) {\n        // 更新本地状态以立即反映UI变化\n        const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n        const book = state.allLorebooks.find(b => b.name === bookName);\n        if (book) {\n            book.enabled = enabled;\n            (0,_store__WEBPACK_IMPORTED_MODULE_1__.updateState)(s => ({ ...s, allLorebooks: [...state.allLorebooks] }));\n        }\n    }\n};\n// --- Regex CRUD Operations ---\n/**\n * 提交正则表达式列表的更改。\n * @param updatedList 要提交的完整正则表达式列表 (仅限UI来源)\n */\nconst commitRegexChanges = async (updatedList) => {\n    const result = await _api__WEBPACK_IMPORTED_MODULE_0__.TavernAPI.replaceRegexes(updatedList);\n    if (result !== null) {\n        // 成功后，重新加载所有数据以确保与后端完全同步\n        // 这是最简单可靠的方式，避免复杂的本地状态管理\n        await loadAllData();\n    }\n};\n/**\n * 创建一个新的正则表达式。\n * @param newRegexData 要创建的正则表达式的数据\n */\nconst createRegex = async (newRegexData) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    // 只处理UI来源的正则\n    const currentUIRegexes = [\n        ...state.regexes.global.filter(r => r.source !== 'card'),\n        ...state.regexes.character.filter(r => r.source !== 'card'),\n    ];\n    const finalNewRegex = {\n        id: `ui-${Date.now()}`,\n        enabled: true,\n        source: 'ui',\n        ...newRegexData,\n    };\n    await commitRegexChanges([...currentUIRegexes, finalNewRegex]);\n};\n/**\n * 更新一个现有的正则表达式。\n * @param regexId 要更新的正则表达式的ID\n * @param updates 要应用的更新\n */\nconst updateRegex = async (regexId, updates) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const currentUIRegexes = [\n        ...state.regexes.global.filter(r => r.source !== 'card'),\n        ...state.regexes.character.filter(r => r.source !== 'card'),\n    ];\n    const updatedList = currentUIRegexes.map(r => (r.id === regexId ? { ...r, ...updates } : r));\n    await commitRegexChanges(updatedList);\n};\n/**\n * 删除一个正则表达式。\n * @param regexId 要删除的正则表达式的ID\n */\nconst deleteRegex = async (regexId) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const currentUIRegexes = [\n        ...state.regexes.global.filter(r => r.source !== 'card'),\n        ...state.regexes.character.filter(r => r.source !== 'card'),\n    ];\n    const updatedList = currentUIRegexes.filter(r => r.id !== regexId);\n    await commitRegexChanges(updatedList);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvd29ybGRfaW5mb19vcHRpbWl6ZXIvY29yZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUEsbUNBQW1DO0FBRUQ7QUFDMEI7QUFHNUQ7OztHQUdHO0FBQ0ksTUFBTSxXQUFXLEdBQUcsS0FBSyxJQUFJLEVBQUU7SUFDcEMsTUFBTSxZQUFZLEdBQUcsZ0RBQVEsRUFBRSxDQUFDO0lBRWhDLFNBQVM7SUFDVCxJQUFJLFlBQVksQ0FBQyxTQUFTLEVBQUUsQ0FBQztRQUMzQixPQUFPLENBQUMsR0FBRyxDQUFDLDBEQUEwRCxDQUFDLENBQUM7UUFDeEUsT0FBTztJQUNULENBQUM7SUFFRCxPQUFPO0lBQ1AsbURBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUM7UUFDaEIsR0FBRyxDQUFDO1FBQ0osWUFBWSxFQUFFLEtBQUs7UUFDbkIsU0FBUyxFQUFFLElBQUk7UUFDZixTQUFTLEVBQUUsSUFBSTtLQUNoQixDQUFDLENBQUMsQ0FBQztJQUVKLElBQUksQ0FBQztRQUNILE1BQU0sT0FBTyxHQUFHLDJDQUFTLENBQUMsVUFBVSxFQUFFLENBQUM7UUFDdkMsTUFBTSxFQUFFLFVBQVUsRUFBRSxhQUFhLEVBQUUsV0FBVyxFQUFFLE1BQU0sRUFBRSxHQUFHLE9BQU8sQ0FBQztRQUNuRSxNQUFNLGtCQUFrQixHQUFHLFdBQVcsS0FBSyxTQUFTLElBQUksV0FBVyxLQUFLLElBQUksQ0FBQztRQUM3RSxNQUFNLGFBQWEsR0FBRyxNQUFNLEtBQUssU0FBUyxJQUFJLE1BQU0sS0FBSyxJQUFJLENBQUM7UUFFOUQsbUJBQW1CO1FBQ25CLE1BQU0sT0FBTyxHQUFHLE1BQU0sT0FBTyxDQUFDLFVBQVUsQ0FBQztZQUN2QywyQ0FBUyxDQUFDLFVBQVUsRUFBRTtZQUN0QiwyQ0FBUyxDQUFDLG1CQUFtQixFQUFFO1lBQy9CLDJDQUFTLENBQUMsWUFBWSxFQUFFO1lBQ3hCLGtCQUFrQixDQUFDLENBQUMsQ0FBQywyQ0FBUyxDQUFDLFdBQVcsRUFBRSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQztZQUNwRSxrQkFBa0IsQ0FBQyxDQUFDLENBQUMsMkNBQVMsQ0FBQyx1QkFBdUIsRUFBRSxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsT0FBTyxDQUFDLElBQUksQ0FBQztZQUNoRixhQUFhLENBQUMsQ0FBQyxDQUFDLDJDQUFTLENBQUMsZUFBZSxFQUFFLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxPQUFPLENBQUMsSUFBSSxDQUFDO1NBQ3BFLENBQUMsQ0FBQztRQUVILDZCQUE2QjtRQUM3QixNQUFNLFlBQVksR0FBRyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLFdBQVcsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDO1FBQy9FLE1BQU0sY0FBYyxHQUFHLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssV0FBVyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUM7UUFDakYsTUFBTSxnQkFBZ0IsR0FBRyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLFdBQVcsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDO1FBQ25GLE1BQU0sUUFBUSxHQUFHLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssV0FBVyxDQUFDLENBQUMsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLENBQUMsQ0FBQyxJQUFJLENBQUM7UUFDN0UsTUFBTSxlQUFlLEdBQUcsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxXQUFXLENBQUMsQ0FBQyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsQ0FBQyxLQUFLLENBQUMsQ0FBQyxDQUFDLElBQUksQ0FBQztRQUNwRixNQUFNLFlBQVksR0FBRyxPQUFPLENBQUMsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLFdBQVcsQ0FBQyxDQUFDLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxDQUFDLEtBQUssQ0FBQyxDQUFDLENBQUMsSUFBSSxDQUFDO1FBRWpGLGVBQWU7UUFFZixhQUFhO1FBQ2IsTUFBTSxhQUFhLEdBQUcsQ0FBQyxZQUFZLElBQUksRUFBRSxDQUFDLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEtBQUssS0FBSyxRQUFRLENBQUMsQ0FBQztRQUM3RSxNQUFNLGdCQUFnQixHQUFHLHVCQUF1QixDQUFDLFlBQVksRUFBRSxRQUFRLENBQUMsQ0FBQztRQUV6RSxnQkFBZ0I7UUFDaEIsTUFBTSxrQkFBa0IsR0FBRyxJQUFJLEdBQUcsQ0FBQyxjQUFjLEVBQUUseUJBQXlCLElBQUksRUFBRSxDQUFDLENBQUM7UUFDcEYsTUFBTSxZQUFZLEdBQUcsQ0FBQyxnQkFBZ0IsSUFBSSxFQUFFLENBQUMsQ0FBQyxHQUFHLENBQUMsSUFBSSxDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQ3pELElBQUksRUFBRSxJQUFJO1lBQ1YsT0FBTyxFQUFFLGtCQUFrQixDQUFDLEdBQUcsQ0FBQyxJQUFJLENBQUM7U0FDdEMsQ0FBQyxDQUFDLENBQUM7UUFFSixnQkFBZ0I7UUFDaEIsTUFBTSxXQUFXLEdBQUcsSUFBSSxHQUFHLEVBQVUsQ0FBQztRQUN0QyxJQUFJLGVBQWUsRUFBRSxPQUFPO1lBQUUsV0FBVyxDQUFDLEdBQUcsQ0FBQyxlQUFlLENBQUMsT0FBTyxDQUFDLENBQUM7UUFDdkUsSUFBSSxlQUFlLEVBQUUsVUFBVTtZQUFFLGVBQWUsQ0FBQyxVQUFVLENBQUMsT0FBTyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsV0FBVyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDO1FBQzdGLE1BQU0sa0JBQWtCLEdBQUcsS0FBSyxDQUFDLElBQUksQ0FBQyxXQUFXLENBQUMsQ0FBQztRQUVuRCxxQ0FBcUM7UUFDckMsTUFBTSxhQUFhLEdBQUcsSUFBSSxHQUFHLEVBQW9CLENBQUM7UUFDbEQsTUFBTSxjQUFjLEdBQUcsSUFBSSxHQUFHLENBQVMsZ0JBQWdCLElBQUksRUFBRSxDQUFDLENBQUM7UUFFL0QsSUFBSSxLQUFLLENBQUMsT0FBTyxDQUFDLGFBQWEsQ0FBQyxFQUFFLENBQUM7WUFDakMsS0FBSyxNQUFNLElBQUksSUFBSSxhQUFhLEVBQUUsQ0FBQztnQkFDakMsSUFBSSxDQUFDLElBQUksRUFBRSxJQUFJO29CQUFFLFNBQVM7Z0JBQzFCLGlEQUFpRDtnQkFDakQsMkJBQTJCO2dCQUMzQixNQUFNLEtBQUssR0FBRyxNQUFNLDJDQUFTLENBQUMsZ0JBQWdCLENBQUMsRUFBRSxJQUFJLEVBQUUsSUFBSSxDQUFDLElBQUksRUFBRSxDQUFDLENBQUM7Z0JBQ3BFLE1BQU0sU0FBUyxHQUFHLElBQUksR0FBRyxFQUFVLENBQUM7Z0JBQ3BDLElBQUksS0FBSyxFQUFFLE9BQU87b0JBQUUsU0FBUyxDQUFDLEdBQUcsQ0FBQyxLQUFLLENBQUMsT0FBTyxDQUFDLENBQUM7Z0JBQ2pELElBQUksS0FBSyxFQUFFLFVBQVU7b0JBQUUsS0FBSyxDQUFDLFVBQVUsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxTQUFTLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7Z0JBRXZFLFNBQVMsQ0FBQyxPQUFPLENBQUMsUUFBUSxDQUFDLEVBQUU7b0JBQzNCLElBQUksQ0FBQyxhQUFhLENBQUMsR0FBRyxDQUFDLFFBQVEsQ0FBQyxFQUFFLENBQUM7d0JBQ2pDLGFBQWEsQ0FBQyxHQUFHLENBQUMsUUFBUSxFQUFFLEVBQUUsQ0FBQyxDQUFDO29CQUNsQyxDQUFDO29CQUNELGFBQWEsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLEVBQUUsSUFBSSxDQUFDLElBQUksQ0FBQyxJQUFJLENBQUMsQ0FBQztvQkFDN0MsY0FBYyxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQyxDQUFDLGdCQUFnQjtnQkFDaEQsQ0FBQyxDQUFDLENBQUM7WUFDTCxDQUFDO1FBQ0gsQ0FBQztRQUVELG9CQUFvQjtRQUNwQixrQkFBa0IsQ0FBQyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxjQUFjLENBQUMsR0FBRyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUM7UUFDdkQsSUFBSSxZQUFZLEVBQUUsQ0FBQztZQUNqQixjQUFjLENBQUMsR0FBRyxDQUFDLFlBQVksQ0FBQyxDQUFDO1FBQ25DLENBQUM7UUFFRCxrQkFBa0I7UUFDbEIsTUFBTSxlQUFlLEdBQUcsSUFBSSxHQUFHLEVBQTJCLENBQUM7UUFDM0QsTUFBTSxhQUFhLEdBQUcsS0FBSyxDQUFDLElBQUksQ0FBQyxjQUFjLENBQUMsQ0FBQyxHQUFHLENBQUMsS0FBSyxFQUFDLElBQUksRUFBQyxFQUFFO1lBQ2hFLE1BQU0sT0FBTyxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxJQUFJLENBQUMsQ0FBQztZQUN6RCxJQUFJLE9BQU8sRUFBRSxDQUFDO2dCQUNaLGVBQWUsQ0FBQyxHQUFHLENBQUMsSUFBSSxFQUFFLE9BQU8sQ0FBQyxDQUFDO1lBQ3JDLENBQUM7UUFDSCxDQUFDLENBQUMsQ0FBQztRQUNILE1BQU0sT0FBTyxDQUFDLEdBQUcsQ0FBQyxhQUFhLENBQUMsQ0FBQztRQUVqQyxZQUFZO1FBQ1osa0RBQVUsQ0FBQztZQUNULGFBQWE7WUFDYixnQkFBZ0I7WUFDaEIsWUFBWTtZQUNaLGtCQUFrQjtZQUNsQixZQUFZLEVBQUUsWUFBWSxJQUFJLElBQUk7WUFDbEMsZUFBZTtZQUNmLGFBQWE7U0FDZCxDQUFDLENBQUM7SUFDTCxDQUFDO0lBQUMsT0FBTyxLQUFLLEVBQUUsQ0FBQztRQUNmLE9BQU8sQ0FBQyxLQUFLLENBQUMscUNBQXFDLEVBQUUsS0FBSyxDQUFDLENBQUM7UUFDNUQsTUFBTSxZQUFZLEdBQUcsS0FBSyxZQUFZLEtBQUssQ0FBQyxDQUFDLENBQUMsS0FBSyxDQUFDLE9BQU8sQ0FBQyxDQUFDLENBQUMsTUFBTSxDQUFDO1FBQ3JFLG1EQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDO1lBQ2hCLEdBQUcsQ0FBQztZQUNKLFlBQVksRUFBRSxLQUFLLEVBQUUsVUFBVTtZQUMvQixTQUFTLEVBQUUsS0FBSztZQUNoQixTQUFTLEVBQUUsV0FBVyxZQUFZLEVBQUU7U0FDckMsQ0FBQyxDQUFDLENBQUM7SUFDTixDQUFDO1lBQVMsQ0FBQztRQUNULGNBQWM7UUFDZCxtREFBVyxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxFQUFFLEdBQUcsQ0FBQyxFQUFFLFNBQVMsRUFBRSxLQUFLLEVBQUUsQ0FBQyxDQUFDLENBQUM7SUFDakQsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGOzs7OztHQUtHO0FBQ0gsU0FBUyx1QkFBdUIsQ0FBQyxZQUFrQyxFQUFFLFFBQWE7SUFDaEYsTUFBTSxrQkFBa0IsR0FBRyxZQUFZLEVBQUUsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLEtBQUssS0FBSyxXQUFXLENBQUMsSUFBSSxFQUFFLENBQUM7SUFDcEYsSUFBSSxXQUFXLEdBQWtCLEVBQUUsQ0FBQztJQUVwQyxJQUFJLFFBQVEsSUFBSSwyQ0FBUyxDQUFDLFNBQVMsRUFBRSxDQUFDO1FBQ3BDLElBQUksQ0FBQztZQUNILE1BQU0sU0FBUyxHQUFHLElBQUksMkNBQVMsQ0FBQyxTQUFTLENBQUMsUUFBUSxDQUFDLENBQUM7WUFDcEQsV0FBVyxHQUFHLENBQUMsU0FBUyxDQUFDLGVBQWUsRUFBRSxJQUFJLEVBQUUsQ0FBQyxDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLEVBQUUsRUFBRSxDQUFDLENBQUM7Z0JBQy9ELEVBQUUsRUFBRSxDQUFDLENBQUMsRUFBRSxJQUFJLFFBQVEsSUFBSSxDQUFDLEdBQUcsRUFBRSxJQUFJLENBQUMsRUFBRTtnQkFDckMsV0FBVyxFQUFFLENBQUMsQ0FBQyxVQUFVLElBQUksU0FBUztnQkFDdEMsVUFBVSxFQUFFLENBQUMsQ0FBQyxTQUFTO2dCQUN2QixjQUFjLEVBQUUsQ0FBQyxDQUFDLGFBQWE7Z0JBQy9CLE9BQU8sRUFBRSxDQUFDLENBQUMsQ0FBQyxRQUFRO2dCQUNwQixLQUFLLEVBQUUsV0FBVztnQkFDbEIsTUFBTSxFQUFFLE1BQU07YUFDZixDQUFDLENBQUMsQ0FBQztRQUNOLENBQUM7UUFBQyxPQUFPLENBQUMsRUFBRSxDQUFDO1lBQ1gsT0FBTyxDQUFDLElBQUksQ0FBQyx5REFBeUQsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUM3RSxDQUFDO0lBQ0gsQ0FBQztJQUVELGdCQUFnQjtJQUNoQixNQUFNLGtCQUFrQixHQUFHLElBQUksR0FBRyxDQUNoQyxrQkFBa0IsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxXQUFXLEtBQUssQ0FBQyxDQUFDLFVBQVUsS0FBSyxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUMsQ0FDdEYsQ0FBQztJQUNGLE1BQU0saUJBQWlCLEdBQUcsV0FBVyxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRTtRQUMvQyxNQUFNLFVBQVUsR0FBRyxHQUFHLENBQUMsQ0FBQyxXQUFXLEtBQUssQ0FBQyxDQUFDLFVBQVUsS0FBSyxDQUFDLENBQUMsY0FBYyxFQUFFLENBQUM7UUFDNUUsT0FBTyxDQUFDLGtCQUFrQixDQUFDLEdBQUcsQ0FBQyxVQUFVLENBQUMsQ0FBQztJQUM3QyxDQUFDLENBQUMsQ0FBQztJQUVILE9BQU8sQ0FBQyxHQUFHLGtCQUFrQixFQUFFLEdBQUcsaUJBQWlCLENBQUMsQ0FBQztBQUN2RCxDQUFDO0FBRUQsbUNBQW1DO0FBRW5DOzs7R0FHRztBQUNJLE1BQU0sY0FBYyxHQUFHLEtBQUssRUFBRSxRQUFnQixFQUFFLEVBQUU7SUFDdkQsTUFBTSxNQUFNLEdBQUcsTUFBTSwyQ0FBUyxDQUFDLGNBQWMsQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUN4RCxJQUFJLE1BQU0sS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUNwQixNQUFNLFdBQVcsRUFBRSxDQUFDLENBQUMsa0JBQWtCO0lBQ3pDLENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRjs7OztHQUlHO0FBQ0ksTUFBTSxjQUFjLEdBQUcsS0FBSyxFQUFFLE9BQWUsRUFBRSxPQUFlLEVBQUUsRUFBRTtJQUN2RSxlQUFlO0lBQ2YsTUFBTSxPQUFPLEdBQUcsTUFBTSwyQ0FBUyxDQUFDLGtCQUFrQixDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQzVELElBQUksT0FBTyxLQUFLLElBQUksRUFBRSxDQUFDO1FBQ3JCLE1BQU0sSUFBSSxLQUFLLENBQUMsdUNBQXVDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDcEUsQ0FBQztJQUVELFlBQVk7SUFDWixNQUFNLFlBQVksR0FBRyxNQUFNLDJDQUFTLENBQUMsY0FBYyxDQUFDLE9BQU8sQ0FBQyxDQUFDO0lBQzdELElBQUksWUFBWSxLQUFLLElBQUksRUFBRSxDQUFDO1FBQzFCLE1BQU0sSUFBSSxLQUFLLENBQUMsa0NBQWtDLE9BQU8sRUFBRSxDQUFDLENBQUM7SUFDL0QsQ0FBQztJQUVELGNBQWM7SUFDZCxJQUFJLE9BQU8sQ0FBQyxNQUFNLEdBQUcsQ0FBQyxFQUFFLENBQUM7UUFDdkIsTUFBTSxTQUFTLEdBQUcsTUFBTSwyQ0FBUyxDQUFDLGtCQUFrQixDQUFDLE9BQU8sRUFBRSxPQUFPLENBQUMsQ0FBQztRQUN2RSxJQUFJLFNBQVMsS0FBSyxJQUFJLEVBQUUsQ0FBQztZQUN2QixxQkFBcUI7WUFDckIsTUFBTSwyQ0FBUyxDQUFDLGNBQWMsQ0FBQyxPQUFPLENBQUMsQ0FBQztZQUN4QyxNQUFNLElBQUksS0FBSyxDQUFDLDJDQUEyQyxPQUFPLEVBQUUsQ0FBQyxDQUFDO1FBQ3hFLENBQUM7SUFDSCxDQUFDO0lBRUQsVUFBVTtJQUNWLE1BQU0sWUFBWSxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxjQUFjLENBQUMsT0FBTyxDQUFDLENBQUM7SUFDN0QsSUFBSSxZQUFZLEtBQUssSUFBSSxFQUFFLENBQUM7UUFDMUIsb0JBQW9CO1FBQ3BCLE9BQU8sQ0FBQyxJQUFJLENBQUMsa0NBQWtDLE9BQU8sbUJBQW1CLENBQUMsQ0FBQztJQUM3RSxDQUFDO0lBRUQsVUFBVTtJQUNWLE1BQU0sV0FBVyxFQUFFLENBQUM7QUFDdEIsQ0FBQyxDQUFDO0FBRUY7OztHQUdHO0FBQ0ksTUFBTSxjQUFjLEdBQUcsS0FBSyxFQUFFLFFBQWdCLEVBQUUsRUFBRTtJQUN2RCxNQUFNLE1BQU0sR0FBRyxNQUFNLDJDQUFTLENBQUMsY0FBYyxDQUFDLFFBQVEsQ0FBQyxDQUFDO0lBQ3hELElBQUksTUFBTSxLQUFLLElBQUksRUFBRSxDQUFDO1FBQ3BCLE1BQU0sV0FBVyxFQUFFLENBQUM7SUFDdEIsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGLHlDQUF5QztBQUV6Qzs7OztHQUlHO0FBQ0ksTUFBTSxtQkFBbUIsR0FBRyxLQUFLLEVBQUUsUUFBZ0IsRUFBRSxTQUF3QixFQUFFLEVBQUU7SUFDdEYsbUJBQW1CO0lBQ25CLE1BQU0sTUFBTSxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxxQkFBcUIsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxTQUFTLENBQUMsQ0FBQyxDQUFDO0lBQzVFLElBQUksTUFBTSxLQUFLLElBQUksSUFBSSxNQUFNLENBQUMsTUFBTSxHQUFHLENBQUMsRUFBRSxDQUFDO1FBQ3pDLE1BQU0sUUFBUSxHQUFHLGdEQUFRLEVBQUUsQ0FBQztRQUM1QixNQUFNLFFBQVEsR0FBRyxNQUFNLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQyxxQkFBcUI7UUFDakQsTUFBTSxjQUFjLEdBQUcsUUFBUSxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLElBQUksRUFBRSxDQUFDO1FBQ3BFLFFBQVEsQ0FBQyxlQUFlLENBQUMsR0FBRyxDQUFDLFFBQVEsRUFBRSxDQUFDLEdBQUcsY0FBYyxFQUFFLFFBQVEsQ0FBQyxDQUFDLENBQUM7UUFDdEUsbURBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsRUFBRSxlQUFlLEVBQUUsSUFBSSxHQUFHLENBQUMsUUFBUSxDQUFDLGVBQWUsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDO0lBQ25GLENBQUM7QUFDSCxDQUFDLENBQUM7QUFFRjs7Ozs7R0FLRztBQUNJLE1BQU0sbUJBQW1CLEdBQUcsS0FBSyxFQUFFLFFBQWdCLEVBQUUsR0FBVyxFQUFFLE9BQStCLEVBQUUsRUFBRTtJQUMxRyxNQUFNLEtBQUssR0FBRyxnREFBUSxFQUFFLENBQUM7SUFDekIsTUFBTSxPQUFPLEdBQUcsS0FBSyxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDcEQsSUFBSSxDQUFDLE9BQU8sRUFBRSxDQUFDO1FBQ2IsTUFBTSxJQUFJLEtBQUssQ0FBQyx1Q0FBdUMsUUFBUSxFQUFFLENBQUMsQ0FBQztJQUNyRSxDQUFDO0lBRUQsSUFBSSxZQUFZLEdBQUcsS0FBSyxDQUFDO0lBQ3pCLE1BQU0sY0FBYyxHQUFHLE9BQU8sQ0FBQyxHQUFHLENBQUMsS0FBSyxDQUFDLEVBQUU7UUFDekMsSUFBSSxLQUFLLENBQUMsR0FBRyxLQUFLLEdBQUcsRUFBRSxDQUFDO1lBQ3RCLFlBQVksR0FBRyxJQUFJLENBQUM7WUFDcEIsT0FBTyxFQUFFLEdBQUcsS0FBSyxFQUFFLEdBQUcsT0FBTyxFQUFFLENBQUM7UUFDbEMsQ0FBQztRQUNELE9BQU8sS0FBSyxDQUFDO0lBQ2YsQ0FBQyxDQUFDLENBQUM7SUFFSCxJQUFJLFlBQVksRUFBRSxDQUFDO1FBQ2pCLE1BQU0sTUFBTSxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxrQkFBa0IsQ0FBQyxRQUFRLEVBQUUsY0FBYyxDQUFDLENBQUM7UUFDNUUsSUFBSSxNQUFNLEtBQUssSUFBSSxFQUFFLENBQUM7WUFDcEIsS0FBSyxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsUUFBUSxFQUFFLGNBQWMsQ0FBQyxDQUFDO1lBQ3BELG1EQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLEVBQUUsZUFBZSxFQUFFLElBQUksR0FBRyxDQUFDLEtBQUssQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUNoRixDQUFDO0lBQ0gsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGOzs7O0dBSUc7QUFDSSxNQUFNLG1CQUFtQixHQUFHLEtBQUssRUFBRSxRQUFnQixFQUFFLEdBQVcsRUFBRSxFQUFFO0lBQ3pFLE1BQU0sTUFBTSxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxxQkFBcUIsQ0FBQyxRQUFRLEVBQUUsQ0FBQyxHQUFHLENBQUMsQ0FBQyxDQUFDO0lBQ3RFLElBQUksTUFBTSxLQUFLLElBQUksRUFBRSxDQUFDO1FBQ3BCLE1BQU0sS0FBSyxHQUFHLGdEQUFRLEVBQUUsQ0FBQztRQUN6QixNQUFNLE9BQU8sR0FBRyxLQUFLLENBQUMsZUFBZSxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsSUFBSSxFQUFFLENBQUM7UUFDMUQsTUFBTSxlQUFlLEdBQUcsT0FBTyxDQUFDLE1BQU0sQ0FBQyxLQUFLLENBQUMsRUFBRSxDQUFDLEtBQUssQ0FBQyxHQUFHLEtBQUssR0FBRyxDQUFDLENBQUM7UUFDbkUsS0FBSyxDQUFDLGVBQWUsQ0FBQyxHQUFHLENBQUMsUUFBUSxFQUFFLGVBQWUsQ0FBQyxDQUFDO1FBQ3JELG1EQUFXLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLEVBQUUsZUFBZSxFQUFFLElBQUksR0FBRyxDQUFDLEtBQUssQ0FBQyxlQUFlLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztJQUNoRixDQUFDO0FBQ0gsQ0FBQyxDQUFDO0FBRUYsMEJBQTBCO0FBRTFCOzs7O0dBSUc7QUFDSSxNQUFNLHdCQUF3QixHQUFHLEtBQUssRUFBRSxRQUFnQixFQUFFLE9BQWdCLEVBQUUsRUFBRTtJQUNuRixNQUFNLFFBQVEsR0FBRyxDQUFDLE1BQU0sMkNBQVMsQ0FBQyxtQkFBbUIsRUFBRSxDQUFDLElBQUksRUFBRSxDQUFDO0lBQy9ELElBQUksQ0FBQyxRQUFRLENBQUMseUJBQXlCLEVBQUUsQ0FBQztRQUN4QyxRQUFRLENBQUMseUJBQXlCLEdBQUcsRUFBRSxDQUFDO0lBQzFDLENBQUM7SUFFRCxNQUFNLFlBQVksR0FBRyxJQUFJLEdBQUcsQ0FBQyxRQUFRLENBQUMseUJBQXlCLENBQUMsQ0FBQztJQUNqRSxJQUFJLE9BQU8sRUFBRSxDQUFDO1FBQ1osWUFBWSxDQUFDLEdBQUcsQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUM3QixDQUFDO1NBQU0sQ0FBQztRQUNOLFlBQVksQ0FBQyxNQUFNLENBQUMsUUFBUSxDQUFDLENBQUM7SUFDaEMsQ0FBQztJQUNELFFBQVEsQ0FBQyx5QkFBeUIsR0FBRyxLQUFLLENBQUMsSUFBSSxDQUFDLFlBQVksQ0FBQyxDQUFDO0lBRTlELE1BQU0sTUFBTSxHQUFHLE1BQU0sMkNBQVMsQ0FBQyxtQkFBbUIsQ0FBQyxRQUFRLENBQUMsQ0FBQztJQUM3RCxJQUFJLE1BQU0sS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUNwQixrQkFBa0I7UUFDbEIsTUFBTSxLQUFLLEdBQUcsZ0RBQVEsRUFBRSxDQUFDO1FBQ3pCLE1BQU0sSUFBSSxHQUFHLEtBQUssQ0FBQyxZQUFZLENBQUMsSUFBSSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLElBQUksS0FBSyxRQUFRLENBQUMsQ0FBQztRQUMvRCxJQUFJLElBQUksRUFBRSxDQUFDO1lBQ1QsSUFBSSxDQUFDLE9BQU8sR0FBRyxPQUFPLENBQUM7WUFDdkIsbURBQVcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsRUFBRSxHQUFHLENBQUMsRUFBRSxZQUFZLEVBQUUsQ0FBQyxHQUFHLEtBQUssQ0FBQyxZQUFZLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQztRQUN0RSxDQUFDO0lBQ0gsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGLGdDQUFnQztBQUVoQzs7O0dBR0c7QUFDSCxNQUFNLGtCQUFrQixHQUFHLEtBQUssRUFBRSxXQUEwQixFQUFFLEVBQUU7SUFDOUQsTUFBTSxNQUFNLEdBQUcsTUFBTSwyQ0FBUyxDQUFDLGNBQWMsQ0FBQyxXQUFXLENBQUMsQ0FBQztJQUMzRCxJQUFJLE1BQU0sS0FBSyxJQUFJLEVBQUUsQ0FBQztRQUNwQix5QkFBeUI7UUFDekIseUJBQXlCO1FBQ3pCLE1BQU0sV0FBVyxFQUFFLENBQUM7SUFDdEIsQ0FBQztBQUNILENBQUMsQ0FBQztBQUVGOzs7R0FHRztBQUNJLE1BQU0sV0FBVyxHQUFHLEtBQUssRUFBRSxZQUFrQyxFQUFFLEVBQUU7SUFDdEUsTUFBTSxLQUFLLEdBQUcsZ0RBQVEsRUFBRSxDQUFDO0lBQ3pCLGFBQWE7SUFDYixNQUFNLGdCQUFnQixHQUFHO1FBQ3ZCLEdBQUcsS0FBSyxDQUFDLE9BQU8sQ0FBQyxNQUFNLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxNQUFNLENBQUM7UUFDeEQsR0FBRyxLQUFLLENBQUMsT0FBTyxDQUFDLFNBQVMsQ0FBQyxNQUFNLENBQUMsQ0FBQyxDQUFDLEVBQUUsQ0FBQyxDQUFDLENBQUMsTUFBTSxLQUFLLE1BQU0sQ0FBQztLQUM1RCxDQUFDO0lBRUYsTUFBTSxhQUFhLEdBQWdCO1FBQ2pDLEVBQUUsRUFBRSxNQUFNLElBQUksQ0FBQyxHQUFHLEVBQUUsRUFBRTtRQUN0QixPQUFPLEVBQUUsSUFBSTtRQUNiLE1BQU0sRUFBRSxJQUFJO1FBQ1osR0FBRyxZQUFZO0tBQ0QsQ0FBQztJQUVqQixNQUFNLGtCQUFrQixDQUFDLENBQUMsR0FBRyxnQkFBZ0IsRUFBRSxhQUFhLENBQUMsQ0FBQyxDQUFDO0FBQ2pFLENBQUMsQ0FBQztBQUVGOzs7O0dBSUc7QUFDSSxNQUFNLFdBQVcsR0FBRyxLQUFLLEVBQUUsT0FBZSxFQUFFLE9BQTZCLEVBQUUsRUFBRTtJQUNsRixNQUFNLEtBQUssR0FBRyxnREFBUSxFQUFFLENBQUM7SUFDekIsTUFBTSxnQkFBZ0IsR0FBRztRQUN2QixHQUFHLEtBQUssQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssTUFBTSxDQUFDO1FBQ3hELEdBQUcsS0FBSyxDQUFDLE9BQU8sQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxNQUFNLENBQUM7S0FDNUQsQ0FBQztJQUVGLE1BQU0sV0FBVyxHQUFHLGdCQUFnQixDQUFDLEdBQUcsQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxDQUFDLEVBQUUsS0FBSyxPQUFPLENBQUMsQ0FBQyxDQUFDLEVBQUUsR0FBRyxDQUFDLEVBQUUsR0FBRyxPQUFPLEVBQUUsQ0FBQyxDQUFDLENBQUMsQ0FBQyxDQUFDLENBQUMsQ0FBQztJQUU3RixNQUFNLGtCQUFrQixDQUFDLFdBQVcsQ0FBQyxDQUFDO0FBQ3hDLENBQUMsQ0FBQztBQUVGOzs7R0FHRztBQUNJLE1BQU0sV0FBVyxHQUFHLEtBQUssRUFBRSxPQUFlLEVBQUUsRUFBRTtJQUNuRCxNQUFNLEtBQUssR0FBRyxnREFBUSxFQUFFLENBQUM7SUFDekIsTUFBTSxnQkFBZ0IsR0FBRztRQUN2QixHQUFHLEtBQUssQ0FBQyxPQUFPLENBQUMsTUFBTSxDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxNQUFNLEtBQUssTUFBTSxDQUFDO1FBQ3hELEdBQUcsS0FBSyxDQUFDLE9BQU8sQ0FBQyxTQUFTLENBQUMsTUFBTSxDQUFDLENBQUMsQ0FBQyxFQUFFLENBQUMsQ0FBQyxDQUFDLE1BQU0sS0FBSyxNQUFNLENBQUM7S0FDNUQsQ0FBQztJQUVGLE1BQU0sV0FBVyxHQUFHLGdCQUFnQixDQUFDLE1BQU0sQ0FBQyxDQUFDLENBQUMsRUFBRSxDQUFDLENBQUMsQ0FBQyxFQUFFLEtBQUssT0FBTyxDQUFDLENBQUM7SUFFbkUsTUFBTSxrQkFBa0IsQ0FBQyxXQUFXLENBQUMsQ0FBQztBQUN4QyxDQUFDLENBQUMiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly90YXZlcm5faGVscGVyX3RlbXBsYXRlLy4vc3JjL3dvcmxkX2luZm9fb3B0aW1pemVyL2NvcmUudHM/Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIHNyYy93b3JsZF9pbmZvX29wdGltaXplci9jb3JlLnRzXG5cbmltcG9ydCB7IFRhdmVybkFQSSB9IGZyb20gJy4vYXBpJztcbmltcG9ydCB7IGdldFN0YXRlLCBzZXRBbGxEYXRhLCB1cGRhdGVTdGF0ZSB9IGZyb20gJy4vc3RvcmUnO1xuaW1wb3J0IHsgTG9yZWJvb2tFbnRyeSwgVGF2ZXJuUmVnZXggfSBmcm9tICcuL3R5cGVzJztcblxuLyoqXG4gKiDliqDovb3miYDmnInliJ3lp4vmlbDmja7jgIJcbiAqIOi/meaYr+W6lOeUqOWQr+WKqOaXtuiwg+eUqOeahOS4u+imgeaVsOaNruiOt+WPluWHveaVsOOAglxuICovXG5leHBvcnQgY29uc3QgbG9hZEFsbERhdGEgPSBhc3luYyAoKSA9PiB7XG4gIGNvbnN0IGN1cnJlbnRTdGF0ZSA9IGdldFN0YXRlKCk7XG5cbiAgLy8g6Ziy5q2i5bm25Y+R5Yqg6L29XG4gIGlmIChjdXJyZW50U3RhdGUuaXNMb2FkaW5nKSB7XG4gICAgY29uc29sZS5sb2coJ1tXSU8gQ29yZV0gRGF0YSBsb2FkaW5nIGFscmVhZHkgaW4gcHJvZ3Jlc3MsIHNraXBwaW5nLi4uJyk7XG4gICAgcmV0dXJuO1xuICB9XG5cbiAgLy8g5byA5aeL5Yqg6L29XG4gIHVwZGF0ZVN0YXRlKHMgPT4gKHtcbiAgICAuLi5zLFxuICAgIGlzRGF0YUxvYWRlZDogZmFsc2UsXG4gICAgaXNMb2FkaW5nOiB0cnVlLFxuICAgIGxvYWRFcnJvcjogbnVsbCxcbiAgfSkpO1xuXG4gIHRyeSB7XG4gICAgY29uc3QgY29udGV4dCA9IFRhdmVybkFQSS5nZXRDb250ZXh0KCk7XG4gICAgY29uc3QgeyBjaGFyYWN0ZXJzOiBhbGxDaGFyYWN0ZXJzLCBjaGFyYWN0ZXJJZCwgY2hhdElkIH0gPSBjb250ZXh0O1xuICAgIGNvbnN0IGhhc0FjdGl2ZUNoYXJhY3RlciA9IGNoYXJhY3RlcklkICE9PSB1bmRlZmluZWQgJiYgY2hhcmFjdGVySWQgIT09IG51bGw7XG4gICAgY29uc3QgaGFzQWN0aXZlQ2hhdCA9IGNoYXRJZCAhPT0gdW5kZWZpbmVkICYmIGNoYXRJZCAhPT0gbnVsbDtcblxuICAgIC8vIC0tLSDlubbooYzojrflj5bln7rnoYDmlbDmja4gLS0tXG4gICAgY29uc3QgcmVzdWx0cyA9IGF3YWl0IFByb21pc2UuYWxsU2V0dGxlZChbXG4gICAgICBUYXZlcm5BUEkuZ2V0UmVnZXhlcygpLFxuICAgICAgVGF2ZXJuQVBJLmdldExvcmVib29rU2V0dGluZ3MoKSxcbiAgICAgIFRhdmVybkFQSS5nZXRMb3JlYm9va3MoKSxcbiAgICAgIGhhc0FjdGl2ZUNoYXJhY3RlciA/IFRhdmVybkFQSS5nZXRDaGFyRGF0YSgpIDogUHJvbWlzZS5yZXNvbHZlKG51bGwpLFxuICAgICAgaGFzQWN0aXZlQ2hhcmFjdGVyID8gVGF2ZXJuQVBJLmdldEN1cnJlbnRDaGFyTG9yZWJvb2tzKCkgOiBQcm9taXNlLnJlc29sdmUobnVsbCksXG4gICAgICBoYXNBY3RpdmVDaGF0ID8gVGF2ZXJuQVBJLmdldENoYXRMb3JlYm9vaygpIDogUHJvbWlzZS5yZXNvbHZlKG51bGwpLFxuICAgIF0pO1xuXG4gICAgLy8gLS0tIOWuieWFqOWcsOS7jlByb21pc2Xnu5PmnpzkuK3mj5Dlj5bmlbDmja4gLS0tXG4gICAgY29uc3QgYWxsVUlSZWdleGVzID0gcmVzdWx0c1swXS5zdGF0dXMgPT09ICdmdWxmaWxsZWQnID8gcmVzdWx0c1swXS52YWx1ZSA6IFtdO1xuICAgIGNvbnN0IGdsb2JhbFNldHRpbmdzID0gcmVzdWx0c1sxXS5zdGF0dXMgPT09ICdmdWxmaWxsZWQnID8gcmVzdWx0c1sxXS52YWx1ZSA6IHt9O1xuICAgIGNvbnN0IGFsbEJvb2tGaWxlTmFtZXMgPSByZXN1bHRzWzJdLnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyByZXN1bHRzWzJdLnZhbHVlIDogW107XG4gICAgY29uc3QgY2hhckRhdGEgPSByZXN1bHRzWzNdLnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyByZXN1bHRzWzNdLnZhbHVlIDogbnVsbDtcbiAgICBjb25zdCBjaGFyTGlua2VkQm9va3MgPSByZXN1bHRzWzRdLnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyByZXN1bHRzWzRdLnZhbHVlIDogbnVsbDtcbiAgICBjb25zdCBjaGF0TG9yZWJvb2sgPSByZXN1bHRzWzVdLnN0YXR1cyA9PT0gJ2Z1bGZpbGxlZCcgPyByZXN1bHRzWzVdLnZhbHVlIDogbnVsbDtcblxuICAgIC8vIC0tLSDmlbDmja7lpITnkIYgLS0tXG5cbiAgICAvLyAxLiDmraPliJnooajovr7lvI/lpITnkIZcbiAgICBjb25zdCBnbG9iYWxSZWdleGVzID0gKGFsbFVJUmVnZXhlcyB8fCBbXSkuZmlsdGVyKHIgPT4gci5zY29wZSA9PT0gJ2dsb2JhbCcpO1xuICAgIGNvbnN0IGNoYXJhY3RlclJlZ2V4ZXMgPSBwcm9jZXNzQ2hhcmFjdGVyUmVnZXhlcyhhbGxVSVJlZ2V4ZXMsIGNoYXJEYXRhKTtcblxuICAgIC8vIDIuIOS4lueVjOS5puaWh+S7tuWSjOWQr+eUqOeKtuaAgVxuICAgIGNvbnN0IGVuYWJsZWRHbG9iYWxCb29rcyA9IG5ldyBTZXQoZ2xvYmFsU2V0dGluZ3M/LnNlbGVjdGVkX2dsb2JhbF9sb3JlYm9va3MgfHwgW10pO1xuICAgIGNvbnN0IGFsbExvcmVib29rcyA9IChhbGxCb29rRmlsZU5hbWVzIHx8IFtdKS5tYXAobmFtZSA9PiAoe1xuICAgICAgbmFtZTogbmFtZSxcbiAgICAgIGVuYWJsZWQ6IGVuYWJsZWRHbG9iYWxCb29rcy5oYXMobmFtZSksXG4gICAgfSkpO1xuXG4gICAgLy8gMy4g5b2T5YmN6KeS6Imy5YWz6IGU55qE5LiW55WM5LmmXG4gICAgY29uc3QgY2hhckJvb2tTZXQgPSBuZXcgU2V0PHN0cmluZz4oKTtcbiAgICBpZiAoY2hhckxpbmtlZEJvb2tzPy5wcmltYXJ5KSBjaGFyQm9va1NldC5hZGQoY2hhckxpbmtlZEJvb2tzLnByaW1hcnkpO1xuICAgIGlmIChjaGFyTGlua2VkQm9va3M/LmFkZGl0aW9uYWwpIGNoYXJMaW5rZWRCb29rcy5hZGRpdGlvbmFsLmZvckVhY2goYiA9PiBjaGFyQm9va1NldC5hZGQoYikpO1xuICAgIGNvbnN0IGNoYXJhY3RlckxvcmVib29rcyA9IEFycmF5LmZyb20oY2hhckJvb2tTZXQpO1xuXG4gICAgLy8gNC4g6K6h566X5omA5pyJ6KeS6Imy5a+55LiW55WM5Lmm55qE5L2/55So5oOF5Ya1IChsb3JlYm9va1VzYWdlKVxuICAgIGNvbnN0IGxvcmVib29rVXNhZ2UgPSBuZXcgTWFwPHN0cmluZywgc3RyaW5nW10+KCk7XG4gICAgY29uc3Qga25vd25Cb29rTmFtZXMgPSBuZXcgU2V0PHN0cmluZz4oYWxsQm9va0ZpbGVOYW1lcyB8fCBbXSk7XG5cbiAgICBpZiAoQXJyYXkuaXNBcnJheShhbGxDaGFyYWN0ZXJzKSkge1xuICAgICAgZm9yIChjb25zdCBjaGFyIG9mIGFsbENoYXJhY3RlcnMpIHtcbiAgICAgICAgaWYgKCFjaGFyPy5uYW1lKSBjb250aW51ZTtcbiAgICAgICAgLy8g5rOo5oSPOiBnZXRDaGFyTG9yZWJvb2tzIOaYr+WQjOatpei/mOaYr+W8guatpeWPluWGs+S6jlRhdmVybkhlbHBlcueahOWunueOsFxuICAgICAgICAvLyDkuLrkv53pmanotbfop4HvvIzmiJHku6zlgYforr7lroPlj6/og73ov5Tlm57kuIDkuKpQcm9taXNlXG4gICAgICAgIGNvbnN0IGJvb2tzID0gYXdhaXQgVGF2ZXJuQVBJLmdldENoYXJMb3JlYm9va3MoeyBuYW1lOiBjaGFyLm5hbWUgfSk7XG4gICAgICAgIGNvbnN0IGNoYXJCb29rcyA9IG5ldyBTZXQ8c3RyaW5nPigpO1xuICAgICAgICBpZiAoYm9va3M/LnByaW1hcnkpIGNoYXJCb29rcy5hZGQoYm9va3MucHJpbWFyeSk7XG4gICAgICAgIGlmIChib29rcz8uYWRkaXRpb25hbCkgYm9va3MuYWRkaXRpb25hbC5mb3JFYWNoKGIgPT4gY2hhckJvb2tzLmFkZChiKSk7XG5cbiAgICAgICAgY2hhckJvb2tzLmZvckVhY2goYm9va05hbWUgPT4ge1xuICAgICAgICAgIGlmICghbG9yZWJvb2tVc2FnZS5oYXMoYm9va05hbWUpKSB7XG4gICAgICAgICAgICBsb3JlYm9va1VzYWdlLnNldChib29rTmFtZSwgW10pO1xuICAgICAgICAgIH1cbiAgICAgICAgICBsb3JlYm9va1VzYWdlLmdldChib29rTmFtZSk/LnB1c2goY2hhci5uYW1lKTtcbiAgICAgICAgICBrbm93bkJvb2tOYW1lcy5hZGQoYm9va05hbWUpOyAvLyDnoa7kv53miYDmnInooqvkvb/nlKjnmoTkuabpg73ooqvliqDovb1cbiAgICAgICAgfSk7XG4gICAgICB9XG4gICAgfVxuXG4gICAgLy8gNS4g5rGH5oC75omA5pyJ6ZyA6KaB5Yqg6L295p2h55uu55qE5LiW55WM5LmmXG4gICAgY2hhcmFjdGVyTG9yZWJvb2tzLmZvckVhY2goYiA9PiBrbm93bkJvb2tOYW1lcy5hZGQoYikpO1xuICAgIGlmIChjaGF0TG9yZWJvb2spIHtcbiAgICAgIGtub3duQm9va05hbWVzLmFkZChjaGF0TG9yZWJvb2spO1xuICAgIH1cblxuICAgIC8vIDYuIOW5tuihjOWKoOi9veaJgOacieS4lueVjOS5pueahOadoeebrlxuICAgIGNvbnN0IGxvcmVib29rRW50cmllcyA9IG5ldyBNYXA8c3RyaW5nLCBMb3JlYm9va0VudHJ5W10+KCk7XG4gICAgY29uc3QgZW50cnlQcm9taXNlcyA9IEFycmF5LmZyb20oa25vd25Cb29rTmFtZXMpLm1hcChhc3luYyBuYW1lID0+IHtcbiAgICAgIGNvbnN0IGVudHJpZXMgPSBhd2FpdCBUYXZlcm5BUEkuZ2V0TG9yZWJvb2tFbnRyaWVzKG5hbWUpO1xuICAgICAgaWYgKGVudHJpZXMpIHtcbiAgICAgICAgbG9yZWJvb2tFbnRyaWVzLnNldChuYW1lLCBlbnRyaWVzKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgICBhd2FpdCBQcm9taXNlLmFsbChlbnRyeVByb21pc2VzKTtcblxuICAgIC8vIDcuIOabtOaWsOWFqOWxgOeKtuaAgVxuICAgIHNldEFsbERhdGEoe1xuICAgICAgZ2xvYmFsUmVnZXhlcyxcbiAgICAgIGNoYXJhY3RlclJlZ2V4ZXMsXG4gICAgICBhbGxMb3JlYm9va3MsXG4gICAgICBjaGFyYWN0ZXJMb3JlYm9va3MsXG4gICAgICBjaGF0TG9yZWJvb2s6IGNoYXRMb3JlYm9vayB8fCBudWxsLFxuICAgICAgbG9yZWJvb2tFbnRyaWVzLFxuICAgICAgbG9yZWJvb2tVc2FnZSxcbiAgICB9KTtcbiAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICBjb25zb2xlLmVycm9yKCdbV0lPIENvcmVdIEZhaWxlZCB0byBsb2FkIGFsbCBkYXRhOicsIGVycm9yKTtcbiAgICBjb25zdCBlcnJvck1lc3NhZ2UgPSBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICfmnKrnn6XplJnor68nO1xuICAgIHVwZGF0ZVN0YXRlKHMgPT4gKHtcbiAgICAgIC4uLnMsXG4gICAgICBpc0RhdGFMb2FkZWQ6IGZhbHNlLCAvLyDnoa7kv53muIXpmaTml6fmlbDmja5cbiAgICAgIGlzTG9hZGluZzogZmFsc2UsXG4gICAgICBsb2FkRXJyb3I6IGDmlbDmja7liqDovb3lpLHotKU6ICR7ZXJyb3JNZXNzYWdlfWAsXG4gICAgfSkpO1xuICB9IGZpbmFsbHkge1xuICAgIC8vIOehruS/neWKoOi9veeKtuaAgeaAu+aYr+iiq+mHjee9rlxuICAgIHVwZGF0ZVN0YXRlKHMgPT4gKHsgLi4ucywgaXNMb2FkaW5nOiBmYWxzZSB9KSk7XG4gIH1cbn07XG5cbi8qKlxuICog5aSE55CG5ZKM5ZCI5bm25p2l6IeqVUnlkozop5LoibLljaHkuK3nmoTop5LoibLnibnlrprmraPliJnooajovr7lvI/jgIJcbiAqIEBwYXJhbSBhbGxVSVJlZ2V4ZXMg5LuOVGF2ZXJuIEFQSeiOt+WPlueahOaJgOaciVVJ5q2j5YiZXG4gKiBAcGFyYW0gY2hhckRhdGEg5b2T5YmN6KeS6Imy55qE5pWw5o2uXG4gKiBAcmV0dXJucyDlkIjlubblkozljrvph43lkI7nmoTop5LoibLmraPliJnooajovr7lvI/mlbDnu4RcbiAqL1xuZnVuY3Rpb24gcHJvY2Vzc0NoYXJhY3RlclJlZ2V4ZXMoYWxsVUlSZWdleGVzOiBUYXZlcm5SZWdleFtdIHwgbnVsbCwgY2hhckRhdGE6IGFueSk6IFRhdmVyblJlZ2V4W10ge1xuICBjb25zdCBjaGFyYWN0ZXJVSVJlZ2V4ZXMgPSBhbGxVSVJlZ2V4ZXM/LmZpbHRlcihyID0+IHIuc2NvcGUgPT09ICdjaGFyYWN0ZXInKSB8fCBbXTtcbiAgbGV0IGNhcmRSZWdleGVzOiBUYXZlcm5SZWdleFtdID0gW107XG5cbiAgaWYgKGNoYXJEYXRhICYmIFRhdmVybkFQSS5DaGFyYWN0ZXIpIHtcbiAgICB0cnkge1xuICAgICAgY29uc3QgY2hhcmFjdGVyID0gbmV3IFRhdmVybkFQSS5DaGFyYWN0ZXIoY2hhckRhdGEpO1xuICAgICAgY2FyZFJlZ2V4ZXMgPSAoY2hhcmFjdGVyLmdldFJlZ2V4U2NyaXB0cygpIHx8IFtdKS5tYXAoKHIsIGkpID0+ICh7XG4gICAgICAgIGlkOiByLmlkIHx8IGBjYXJkLSR7RGF0ZS5ub3coKX0tJHtpfWAsXG4gICAgICAgIHNjcmlwdF9uYW1lOiByLnNjcmlwdE5hbWUgfHwgJ+acquWRveWQjeWNoeWGheato+WImScsXG4gICAgICAgIGZpbmRfcmVnZXg6IHIuZmluZFJlZ2V4LFxuICAgICAgICByZXBsYWNlX3N0cmluZzogci5yZXBsYWNlU3RyaW5nLFxuICAgICAgICBlbmFibGVkOiAhci5kaXNhYmxlZCxcbiAgICAgICAgc2NvcGU6ICdjaGFyYWN0ZXInLFxuICAgICAgICBzb3VyY2U6ICdjYXJkJyxcbiAgICAgIH0pKTtcbiAgICB9IGNhdGNoIChlKSB7XG4gICAgICBjb25zb2xlLndhcm4oXCJbV0lPIENvcmVdIENvdWxkbid0IHBhcnNlIGNoYXJhY3RlciBjYXJkIHJlZ2V4IHNjcmlwdHM6XCIsIGUpO1xuICAgIH1cbiAgfVxuXG4gIC8vIOWOu+mHjemAu+i+ke+8mlVJ5Lit55qE5q2j5YiZ5LyY5YWIXG4gIGNvbnN0IHVpUmVnZXhJZGVudGlmaWVycyA9IG5ldyBTZXQoXG4gICAgY2hhcmFjdGVyVUlSZWdleGVzLm1hcChyID0+IGAke3Iuc2NyaXB0X25hbWV9Ojoke3IuZmluZF9yZWdleH06OiR7ci5yZXBsYWNlX3N0cmluZ31gKSxcbiAgKTtcbiAgY29uc3QgdW5pcXVlQ2FyZFJlZ2V4ZXMgPSBjYXJkUmVnZXhlcy5maWx0ZXIociA9PiB7XG4gICAgY29uc3QgaWRlbnRpZmllciA9IGAke3Iuc2NyaXB0X25hbWV9Ojoke3IuZmluZF9yZWdleH06OiR7ci5yZXBsYWNlX3N0cmluZ31gO1xuICAgIHJldHVybiAhdWlSZWdleElkZW50aWZpZXJzLmhhcyhpZGVudGlmaWVyKTtcbiAgfSk7XG5cbiAgcmV0dXJuIFsuLi5jaGFyYWN0ZXJVSVJlZ2V4ZXMsIC4uLnVuaXF1ZUNhcmRSZWdleGVzXTtcbn1cblxuLy8gLS0tIExvcmVib29rIENSVUQgT3BlcmF0aW9ucyAtLS1cblxuLyoqXG4gKiDliJvlu7rkuIDkuKrmlrDnmoTnqbrkuJbnlYzkuabjgIJcbiAqIEBwYXJhbSBib29rTmFtZSDmlrDkuJbnlYzkuabnmoTlkI3np7BcbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZUxvcmVib29rID0gYXN5bmMgKGJvb2tOYW1lOiBzdHJpbmcpID0+IHtcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGF2ZXJuQVBJLmNyZWF0ZUxvcmVib29rKGJvb2tOYW1lKTtcbiAgaWYgKHJlc3VsdCAhPT0gbnVsbCkge1xuICAgIGF3YWl0IGxvYWRBbGxEYXRhKCk7IC8vIOmHjeaWsOWKoOi9veaJgOacieaVsOaNruS7peehruS/neeKtuaAgeWQjOatpVxuICB9XG59O1xuXG4vKipcbiAqIOmHjeWRveWQjeS4gOS4quS4lueVjOS5puOAglxuICogQHBhcmFtIG9sZE5hbWUg5pen5ZCN56ewXG4gKiBAcGFyYW0gbmV3TmFtZSDmlrDlkI3np7BcbiAqL1xuZXhwb3J0IGNvbnN0IHJlbmFtZUxvcmVib29rID0gYXN5bmMgKG9sZE5hbWU6IHN0cmluZywgbmV3TmFtZTogc3RyaW5nKSA9PiB7XG4gIC8vIDEuIOiOt+WPluaXp+S5pueahOaJgOacieadoeebrlxuICBjb25zdCBlbnRyaWVzID0gYXdhaXQgVGF2ZXJuQVBJLmdldExvcmVib29rRW50cmllcyhvbGROYW1lKTtcbiAgaWYgKGVudHJpZXMgPT09IG51bGwpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBnZXQgZW50cmllcyBmb3IgbG9yZWJvb2s6ICR7b2xkTmFtZX1gKTtcbiAgfVxuXG4gIC8vIDIuIOWIm+W7uuS4gOS4quaWsOS5plxuICBjb25zdCBjcmVhdGVSZXN1bHQgPSBhd2FpdCBUYXZlcm5BUEkuY3JlYXRlTG9yZWJvb2sobmV3TmFtZSk7XG4gIGlmIChjcmVhdGVSZXN1bHQgPT09IG51bGwpIHtcbiAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBjcmVhdGUgbmV3IGxvcmVib29rOiAke25ld05hbWV9YCk7XG4gIH1cblxuICAvLyAzLiDlsIbmnaHnm67lpI3liLbliLDmlrDkuaZcbiAgaWYgKGVudHJpZXMubGVuZ3RoID4gMCkge1xuICAgIGNvbnN0IHNldFJlc3VsdCA9IGF3YWl0IFRhdmVybkFQSS5zZXRMb3JlYm9va0VudHJpZXMobmV3TmFtZSwgZW50cmllcyk7XG4gICAgaWYgKHNldFJlc3VsdCA9PT0gbnVsbCkge1xuICAgICAgLy8g5riF55CG77ya5aaC5p6c5p2h55uu6K6+572u5aSx6LSl77yM5Yig6Zmk5paw5bu655qE5LmmXG4gICAgICBhd2FpdCBUYXZlcm5BUEkuZGVsZXRlTG9yZWJvb2sobmV3TmFtZSk7XG4gICAgICB0aHJvdyBuZXcgRXJyb3IoYEZhaWxlZCB0byBzZXQgZW50cmllcyBmb3IgbmV3IGxvcmVib29rOiAke25ld05hbWV9YCk7XG4gICAgfVxuICB9XG5cbiAgLy8gNC4g5Yig6Zmk5pen5LmmXG4gIGNvbnN0IGRlbGV0ZVJlc3VsdCA9IGF3YWl0IFRhdmVybkFQSS5kZWxldGVMb3JlYm9vayhvbGROYW1lKTtcbiAgaWYgKGRlbGV0ZVJlc3VsdCA9PT0gbnVsbCkge1xuICAgIC8vIOi/meS4jeaYr+S4gOS4quWFs+mUrumUmeivr++8jOS9huW6lOivpeiusOW9leS4i+adpVxuICAgIGNvbnNvbGUud2FybihgRmFpbGVkIHRvIGRlbGV0ZSBvbGQgbG9yZWJvb2sgXCIke29sZE5hbWV9XCIgYWZ0ZXIgcmVuYW1pbmcuYCk7XG4gIH1cblxuICAvLyA1LiDliLfmlrDmlbDmja5cbiAgYXdhaXQgbG9hZEFsbERhdGEoKTtcbn07XG5cbi8qKlxuICog5Yig6Zmk5LiA5Liq5LiW55WM5Lmm44CCXG4gKiBAcGFyYW0gYm9va05hbWUg6KaB5Yig6Zmk55qE5LiW55WM5Lmm55qE5ZCN56ewXG4gKi9cbmV4cG9ydCBjb25zdCBkZWxldGVMb3JlYm9vayA9IGFzeW5jIChib29rTmFtZTogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IHJlc3VsdCA9IGF3YWl0IFRhdmVybkFQSS5kZWxldGVMb3JlYm9vayhib29rTmFtZSk7XG4gIGlmIChyZXN1bHQgIT09IG51bGwpIHtcbiAgICBhd2FpdCBsb2FkQWxsRGF0YSgpO1xuICB9XG59O1xuXG4vLyAtLS0gTG9yZWJvb2sgRW50cnkgQ1JVRCBPcGVyYXRpb25zIC0tLVxuXG4vKipcbiAqIOWcqOaMh+WumueahOS4lueVjOS5puS4reWIm+W7uuS4gOS4quaWsOadoeebruOAglxuICogQHBhcmFtIGJvb2tOYW1lIOS4lueVjOS5puWQjeensFxuICogQHBhcmFtIGVudHJ5RGF0YSDopoHliJvlu7rnmoTmnaHnm67nmoTmlbDmja5cbiAqL1xuZXhwb3J0IGNvbnN0IGNyZWF0ZUxvcmVib29rRW50cnkgPSBhc3luYyAoYm9va05hbWU6IHN0cmluZywgZW50cnlEYXRhOiBMb3JlYm9va0VudHJ5KSA9PiB7XG4gIC8vIFRhdmVybiBBUEnpnIDopoHkuIDkuKrmlbDnu4RcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGF2ZXJuQVBJLmNyZWF0ZUxvcmVib29rRW50cmllcyhib29rTmFtZSwgW2VudHJ5RGF0YV0pO1xuICBpZiAocmVzdWx0ICE9PSBudWxsICYmIHJlc3VsdC5sZW5ndGggPiAwKSB7XG4gICAgY29uc3QgbmV3U3RhdGUgPSBnZXRTdGF0ZSgpO1xuICAgIGNvbnN0IG5ld0VudHJ5ID0gcmVzdWx0WzBdOyAvLyBBUEnkvJrov5Tlm57liJvlu7rlkI7nmoTmnaHnm67vvIzljIXlkKtVSURcbiAgICBjb25zdCBjdXJyZW50RW50cmllcyA9IG5ld1N0YXRlLmxvcmVib29rRW50cmllcy5nZXQoYm9va05hbWUpIHx8IFtdO1xuICAgIG5ld1N0YXRlLmxvcmVib29rRW50cmllcy5zZXQoYm9va05hbWUsIFsuLi5jdXJyZW50RW50cmllcywgbmV3RW50cnldKTtcbiAgICB1cGRhdGVTdGF0ZShzID0+ICh7IC4uLnMsIGxvcmVib29rRW50cmllczogbmV3IE1hcChuZXdTdGF0ZS5sb3JlYm9va0VudHJpZXMpIH0pKTtcbiAgfVxufTtcblxuLyoqXG4gKiDmm7TmlrDkuJbnlYzkuabkuK3nmoTkuIDkuKrnjrDmnInmnaHnm67jgIJcbiAqIEBwYXJhbSBib29rTmFtZSDkuJbnlYzkuablkI3np7BcbiAqIEBwYXJhbSB1aWQg6KaB5pu05paw55qE5p2h55uu55qE5ZSv5LiASURcbiAqIEBwYXJhbSB1cGRhdGVzIOimgeW6lOeUqOS6juadoeebrueahOmDqOWIhuabtOaWsFxuICovXG5leHBvcnQgY29uc3QgdXBkYXRlTG9yZWJvb2tFbnRyeSA9IGFzeW5jIChib29rTmFtZTogc3RyaW5nLCB1aWQ6IHN0cmluZywgdXBkYXRlczogUGFydGlhbDxMb3JlYm9va0VudHJ5PikgPT4ge1xuICBjb25zdCBzdGF0ZSA9IGdldFN0YXRlKCk7XG4gIGNvbnN0IGVudHJpZXMgPSBzdGF0ZS5sb3JlYm9va0VudHJpZXMuZ2V0KGJvb2tOYW1lKTtcbiAgaWYgKCFlbnRyaWVzKSB7XG4gICAgdGhyb3cgbmV3IEVycm9yKGBbV0lPIENvcmVdIEJvb2sgbm90IGZvdW5kIGluIHN0YXRlOiAke2Jvb2tOYW1lfWApO1xuICB9XG5cbiAgbGV0IGVudHJ5VXBkYXRlZCA9IGZhbHNlO1xuICBjb25zdCB1cGRhdGVkRW50cmllcyA9IGVudHJpZXMubWFwKGVudHJ5ID0+IHtcbiAgICBpZiAoZW50cnkudWlkID09PSB1aWQpIHtcbiAgICAgIGVudHJ5VXBkYXRlZCA9IHRydWU7XG4gICAgICByZXR1cm4geyAuLi5lbnRyeSwgLi4udXBkYXRlcyB9O1xuICAgIH1cbiAgICByZXR1cm4gZW50cnk7XG4gIH0pO1xuXG4gIGlmIChlbnRyeVVwZGF0ZWQpIHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXZlcm5BUEkuc2V0TG9yZWJvb2tFbnRyaWVzKGJvb2tOYW1lLCB1cGRhdGVkRW50cmllcyk7XG4gICAgaWYgKHJlc3VsdCAhPT0gbnVsbCkge1xuICAgICAgc3RhdGUubG9yZWJvb2tFbnRyaWVzLnNldChib29rTmFtZSwgdXBkYXRlZEVudHJpZXMpO1xuICAgICAgdXBkYXRlU3RhdGUocyA9PiAoeyAuLi5zLCBsb3JlYm9va0VudHJpZXM6IG5ldyBNYXAoc3RhdGUubG9yZWJvb2tFbnRyaWVzKSB9KSk7XG4gICAgfVxuICB9XG59O1xuXG4vKipcbiAqIOS7juS4lueVjOS5puS4reWIoOmZpOS4gOS4quadoeebruOAglxuICogQHBhcmFtIGJvb2tOYW1lIOS4lueVjOS5puWQjeensFxuICogQHBhcmFtIHVpZCDopoHliKDpmaTnmoTmnaHnm67nmoTllK/kuIBJRFxuICovXG5leHBvcnQgY29uc3QgZGVsZXRlTG9yZWJvb2tFbnRyeSA9IGFzeW5jIChib29rTmFtZTogc3RyaW5nLCB1aWQ6IHN0cmluZykgPT4ge1xuICBjb25zdCByZXN1bHQgPSBhd2FpdCBUYXZlcm5BUEkuZGVsZXRlTG9yZWJvb2tFbnRyaWVzKGJvb2tOYW1lLCBbdWlkXSk7XG4gIGlmIChyZXN1bHQgIT09IG51bGwpIHtcbiAgICBjb25zdCBzdGF0ZSA9IGdldFN0YXRlKCk7XG4gICAgY29uc3QgZW50cmllcyA9IHN0YXRlLmxvcmVib29rRW50cmllcy5nZXQoYm9va05hbWUpIHx8IFtdO1xuICAgIGNvbnN0IGZpbHRlcmVkRW50cmllcyA9IGVudHJpZXMuZmlsdGVyKGVudHJ5ID0+IGVudHJ5LnVpZCAhPT0gdWlkKTtcbiAgICBzdGF0ZS5sb3JlYm9va0VudHJpZXMuc2V0KGJvb2tOYW1lLCBmaWx0ZXJlZEVudHJpZXMpO1xuICAgIHVwZGF0ZVN0YXRlKHMgPT4gKHsgLi4ucywgbG9yZWJvb2tFbnRyaWVzOiBuZXcgTWFwKHN0YXRlLmxvcmVib29rRW50cmllcykgfSkpO1xuICB9XG59O1xuXG4vLyAtLS0gR2xvYmFsIFNldHRpbmdzIC0tLVxuXG4vKipcbiAqIOiuvue9ruS4gOS4quWFqOWxgOS4lueVjOS5pueahOWQr+eUqOeKtuaAgeOAglxuICogQHBhcmFtIGJvb2tOYW1lIOS4lueVjOS5pueahOWQjeensFxuICogQHBhcmFtIGVuYWJsZWQgdHJ1ZeS4uuWQr+eUqCwgZmFsc2XkuLrnpoHnlKhcbiAqL1xuZXhwb3J0IGNvbnN0IHNldEdsb2JhbExvcmVib29rRW5hYmxlZCA9IGFzeW5jIChib29rTmFtZTogc3RyaW5nLCBlbmFibGVkOiBib29sZWFuKSA9PiB7XG4gIGNvbnN0IHNldHRpbmdzID0gKGF3YWl0IFRhdmVybkFQSS5nZXRMb3JlYm9va1NldHRpbmdzKCkpIHx8IHt9O1xuICBpZiAoIXNldHRpbmdzLnNlbGVjdGVkX2dsb2JhbF9sb3JlYm9va3MpIHtcbiAgICBzZXR0aW5ncy5zZWxlY3RlZF9nbG9iYWxfbG9yZWJvb2tzID0gW107XG4gIH1cblxuICBjb25zdCBlbmFibGVkQm9va3MgPSBuZXcgU2V0KHNldHRpbmdzLnNlbGVjdGVkX2dsb2JhbF9sb3JlYm9va3MpO1xuICBpZiAoZW5hYmxlZCkge1xuICAgIGVuYWJsZWRCb29rcy5hZGQoYm9va05hbWUpO1xuICB9IGVsc2Uge1xuICAgIGVuYWJsZWRCb29rcy5kZWxldGUoYm9va05hbWUpO1xuICB9XG4gIHNldHRpbmdzLnNlbGVjdGVkX2dsb2JhbF9sb3JlYm9va3MgPSBBcnJheS5mcm9tKGVuYWJsZWRCb29rcyk7XG5cbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGF2ZXJuQVBJLnNldExvcmVib29rU2V0dGluZ3Moc2V0dGluZ3MpO1xuICBpZiAocmVzdWx0ICE9PSBudWxsKSB7XG4gICAgLy8g5pu05paw5pys5Zyw54q25oCB5Lul56uL5Y2z5Y+N5pigVUnlj5jljJZcbiAgICBjb25zdCBzdGF0ZSA9IGdldFN0YXRlKCk7XG4gICAgY29uc3QgYm9vayA9IHN0YXRlLmFsbExvcmVib29rcy5maW5kKGIgPT4gYi5uYW1lID09PSBib29rTmFtZSk7XG4gICAgaWYgKGJvb2spIHtcbiAgICAgIGJvb2suZW5hYmxlZCA9IGVuYWJsZWQ7XG4gICAgICB1cGRhdGVTdGF0ZShzID0+ICh7IC4uLnMsIGFsbExvcmVib29rczogWy4uLnN0YXRlLmFsbExvcmVib29rc10gfSkpO1xuICAgIH1cbiAgfVxufTtcblxuLy8gLS0tIFJlZ2V4IENSVUQgT3BlcmF0aW9ucyAtLS1cblxuLyoqXG4gKiDmj5DkuqTmraPliJnooajovr7lvI/liJfooajnmoTmm7TmlLnjgIJcbiAqIEBwYXJhbSB1cGRhdGVkTGlzdCDopoHmj5DkuqTnmoTlrozmlbTmraPliJnooajovr7lvI/liJfooaggKOS7hemZkFVJ5p2l5rqQKVxuICovXG5jb25zdCBjb21taXRSZWdleENoYW5nZXMgPSBhc3luYyAodXBkYXRlZExpc3Q6IFRhdmVyblJlZ2V4W10pID0+IHtcbiAgY29uc3QgcmVzdWx0ID0gYXdhaXQgVGF2ZXJuQVBJLnJlcGxhY2VSZWdleGVzKHVwZGF0ZWRMaXN0KTtcbiAgaWYgKHJlc3VsdCAhPT0gbnVsbCkge1xuICAgIC8vIOaIkOWKn+WQju+8jOmHjeaWsOWKoOi9veaJgOacieaVsOaNruS7peehruS/neS4juWQjuerr+WujOWFqOWQjOatpVxuICAgIC8vIOi/meaYr+acgOeugOWNleWPr+mdoOeahOaWueW8j++8jOmBv+WFjeWkjeadgueahOacrOWcsOeKtuaAgeeuoeeQhlxuICAgIGF3YWl0IGxvYWRBbGxEYXRhKCk7XG4gIH1cbn07XG5cbi8qKlxuICog5Yib5bu65LiA5Liq5paw55qE5q2j5YiZ6KGo6L6+5byP44CCXG4gKiBAcGFyYW0gbmV3UmVnZXhEYXRhIOimgeWIm+W7uueahOato+WImeihqOi+vuW8j+eahOaVsOaNrlxuICovXG5leHBvcnQgY29uc3QgY3JlYXRlUmVnZXggPSBhc3luYyAobmV3UmVnZXhEYXRhOiBQYXJ0aWFsPFRhdmVyblJlZ2V4PikgPT4ge1xuICBjb25zdCBzdGF0ZSA9IGdldFN0YXRlKCk7XG4gIC8vIOWPquWkhOeQhlVJ5p2l5rqQ55qE5q2j5YiZXG4gIGNvbnN0IGN1cnJlbnRVSVJlZ2V4ZXMgPSBbXG4gICAgLi4uc3RhdGUucmVnZXhlcy5nbG9iYWwuZmlsdGVyKHIgPT4gci5zb3VyY2UgIT09ICdjYXJkJyksXG4gICAgLi4uc3RhdGUucmVnZXhlcy5jaGFyYWN0ZXIuZmlsdGVyKHIgPT4gci5zb3VyY2UgIT09ICdjYXJkJyksXG4gIF07XG5cbiAgY29uc3QgZmluYWxOZXdSZWdleDogVGF2ZXJuUmVnZXggPSB7XG4gICAgaWQ6IGB1aS0ke0RhdGUubm93KCl9YCxcbiAgICBlbmFibGVkOiB0cnVlLFxuICAgIHNvdXJjZTogJ3VpJyxcbiAgICAuLi5uZXdSZWdleERhdGEsXG4gIH0gYXMgVGF2ZXJuUmVnZXg7XG5cbiAgYXdhaXQgY29tbWl0UmVnZXhDaGFuZ2VzKFsuLi5jdXJyZW50VUlSZWdleGVzLCBmaW5hbE5ld1JlZ2V4XSk7XG59O1xuXG4vKipcbiAqIOabtOaWsOS4gOS4queOsOacieeahOato+WImeihqOi+vuW8j+OAglxuICogQHBhcmFtIHJlZ2V4SWQg6KaB5pu05paw55qE5q2j5YiZ6KGo6L6+5byP55qESURcbiAqIEBwYXJhbSB1cGRhdGVzIOimgeW6lOeUqOeahOabtOaWsFxuICovXG5leHBvcnQgY29uc3QgdXBkYXRlUmVnZXggPSBhc3luYyAocmVnZXhJZDogc3RyaW5nLCB1cGRhdGVzOiBQYXJ0aWFsPFRhdmVyblJlZ2V4PikgPT4ge1xuICBjb25zdCBzdGF0ZSA9IGdldFN0YXRlKCk7XG4gIGNvbnN0IGN1cnJlbnRVSVJlZ2V4ZXMgPSBbXG4gICAgLi4uc3RhdGUucmVnZXhlcy5nbG9iYWwuZmlsdGVyKHIgPT4gci5zb3VyY2UgIT09ICdjYXJkJyksXG4gICAgLi4uc3RhdGUucmVnZXhlcy5jaGFyYWN0ZXIuZmlsdGVyKHIgPT4gci5zb3VyY2UgIT09ICdjYXJkJyksXG4gIF07XG5cbiAgY29uc3QgdXBkYXRlZExpc3QgPSBjdXJyZW50VUlSZWdleGVzLm1hcChyID0+IChyLmlkID09PSByZWdleElkID8geyAuLi5yLCAuLi51cGRhdGVzIH0gOiByKSk7XG5cbiAgYXdhaXQgY29tbWl0UmVnZXhDaGFuZ2VzKHVwZGF0ZWRMaXN0KTtcbn07XG5cbi8qKlxuICog5Yig6Zmk5LiA5Liq5q2j5YiZ6KGo6L6+5byP44CCXG4gKiBAcGFyYW0gcmVnZXhJZCDopoHliKDpmaTnmoTmraPliJnooajovr7lvI/nmoRJRFxuICovXG5leHBvcnQgY29uc3QgZGVsZXRlUmVnZXggPSBhc3luYyAocmVnZXhJZDogc3RyaW5nKSA9PiB7XG4gIGNvbnN0IHN0YXRlID0gZ2V0U3RhdGUoKTtcbiAgY29uc3QgY3VycmVudFVJUmVnZXhlcyA9IFtcbiAgICAuLi5zdGF0ZS5yZWdleGVzLmdsb2JhbC5maWx0ZXIociA9PiByLnNvdXJjZSAhPT0gJ2NhcmQnKSxcbiAgICAuLi5zdGF0ZS5yZWdleGVzLmNoYXJhY3Rlci5maWx0ZXIociA9PiByLnNvdXJjZSAhPT0gJ2NhcmQnKSxcbiAgXTtcblxuICBjb25zdCB1cGRhdGVkTGlzdCA9IGN1cnJlbnRVSVJlZ2V4ZXMuZmlsdGVyKHIgPT4gci5pZCAhPT0gcmVnZXhJZCk7XG5cbiAgYXdhaXQgY29tbWl0UmVnZXhDaGFuZ2VzKHVwZGF0ZWRMaXN0KTtcbn07XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/core.ts\n\n}");
  },
  "./src/world_info_optimizer/events.ts": 
  /*!********************************************!*\
  !*** ./src/world_info_optimizer/events.ts ***!
  \********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initializeEventHandlers: () => (/* binding */ initializeEventHandlers)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"./src/world_info_optimizer/constants.ts\");\n/* harmony import */ var _core__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./core */ \"./src/world_info_optimizer/core.ts\");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./store */ \"./src/world_info_optimizer/store.ts\");\n/* harmony import */ var _ui_modals__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ui/modals */ \"./src/world_info_optimizer/ui/modals.ts\");\n// src/world_info_optimizer/events.ts\n\n\n\n\nlet parentDoc;\nlet $;\n/**\n * 初始化所有UI事件处理器。\n * 这个函数应该在UI注入DOM后调用。\n * @param parentWindow 父窗口对象\n */\nconst initializeEventHandlers = (parentWindow) => {\n    parentDoc = parentWindow.document;\n    $ = parentWindow.jQuery;\n    const $body = $('body', parentDoc);\n    // --- 主面板和按钮的事件 ---\n    // 打开面板的按钮\n    $body.on('click', `#${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}`, () => {\n        $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc).fadeIn(200);\n        // 首次打开时加载数据\n        if (!(0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)().isDataLoaded) {\n            (0,_core__WEBPACK_IMPORTED_MODULE_1__.loadAllData)();\n        }\n    });\n    // 关闭面板的按钮\n    $body.on('click', `#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID} .wio-close-btn`, () => {\n        $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc).fadeOut(200);\n    });\n    // --- 事件委托：主面板内的所有点击事件 ---\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc);\n    $panel.on('click', async (event) => {\n        const $target = $(event.target);\n        // --- 标签页切换 ---\n        const tabButton = $target.closest('.wio-tab-btn');\n        if (tabButton.length) {\n            const tabId = tabButton.data('tab-id');\n            (0,_store__WEBPACK_IMPORTED_MODULE_2__.setActiveTab)(tabId);\n            return;\n        }\n        // --- 工具栏操作 ---\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.CREATE_LOREBOOK_BTN_ID}`).length) {\n            handleCreateBook();\n            return;\n        }\n        if ($target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}`).length || $target.closest(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}-retry`).length) {\n            (0,_core__WEBPACK_IMPORTED_MODULE_1__.loadAllData)();\n            return;\n        }\n        // --- 世界书操作 ---\n        const renameBookBtn = $target.closest('.wio-rename-book-btn');\n        if (renameBookBtn.length) {\n            const bookName = renameBookBtn.closest('.wio-book-group').data('book-name');\n            handleRenameBook(bookName);\n            return;\n        }\n        const deleteBookBtn = $target.closest('.wio-delete-book-btn');\n        if (deleteBookBtn.length) {\n            const bookName = deleteBookBtn.closest('.wio-book-group').data('book-name');\n            handleDeleteBook(bookName);\n            return;\n        }\n        // --- 条目操作 ---\n        const createEntryBtn = $target.closest('.wio-create-entry-btn');\n        if (createEntryBtn.length) {\n            const bookName = createEntryBtn.data('book-name');\n            handleCreateEntry(bookName);\n            return;\n        }\n        const editEntryBtn = $target.closest('.wio-edit-entry-btn');\n        if (editEntryBtn.length) {\n            const entryItem = editEntryBtn.closest('.wio-entry-item');\n            const bookName = entryItem.data('book-name');\n            const uid = entryItem.data('uid');\n            handleEditEntry(bookName, uid);\n            return;\n        }\n        const deleteEntryBtn = $target.closest('.wio-delete-entry-btn');\n        if (deleteEntryBtn.length) {\n            const entryItem = deleteEntryBtn.closest('.wio-entry-item');\n            const bookName = entryItem.data('book-name');\n            const uid = entryItem.data('uid');\n            handleDeleteEntry(bookName, uid);\n            return;\n        }\n        // --- 正则操作 ---\n        const createRegexBtn = $target.closest('.wio-create-regex-btn');\n        if (createRegexBtn.length) {\n            const scope = createRegexBtn.data('scope');\n            handleCreateRegex(scope);\n            return;\n        }\n        const editRegexBtn = $target.closest('.wio-edit-regex-btn');\n        if (editRegexBtn.length) {\n            const regexItem = editRegexBtn.closest('.wio-regex-item');\n            const regexId = regexItem.data('id');\n            handleEditRegex(regexId);\n            return;\n        }\n        const deleteRegexBtn = $target.closest('.wio-delete-regex-btn');\n        if (deleteRegexBtn.length) {\n            const regexItem = deleteRegexBtn.closest('.wio-regex-item');\n            const regexId = regexItem.data('id');\n            handleDeleteRegex(regexId);\n            return;\n        }\n    });\n    // --- 事件委托：处理表单元素的 change 事件 ---\n    $panel.on('change', async (event) => {\n        const $target = $(event.target);\n        // --- 全局世界书启用/禁用切换 ---\n        if ($target.is('.wio-global-book-toggle')) {\n            const bookName = $target.closest('.wio-book-group').data('book-name');\n            const isEnabled = $target.prop('checked');\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.setGlobalLorebookEnabled)(bookName, isEnabled);\n            return;\n        }\n        // --- 条目启用/禁用切换 ---\n        if ($target.is('.wio-entry-toggle')) {\n            const entryItem = $target.closest('.wio-entry-item');\n            const bookName = entryItem.data('book-name');\n            const uid = entryItem.data('uid');\n            const isEnabled = $target.prop('checked');\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateLorebookEntry)(bookName, uid, { enabled: isEnabled });\n            return;\n        }\n        // --- 正则启用/禁用切换 ---\n        if ($target.is('.wio-regex-toggle')) {\n            const regexItem = $target.closest('.wio-regex-item');\n            const regexId = regexItem.data('id');\n            const isEnabled = $target.prop('checked');\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateRegex)(regexId, { enabled: isEnabled });\n            return;\n        }\n    });\n    // --- 事件委托：处理搜索框输入 ---\n    $panel.on('input', `#${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}`, event => {\n        const query = $(event.target).val();\n        (0,_store__WEBPACK_IMPORTED_MODULE_2__.setSearchQuery)(query);\n    });\n};\n// --- 事件处理器具体实现 ---\n/**\n * 处理重命名世界书的逻辑。\n * @param bookName 要重命名的世界书的当前名称\n */\nconst handleRenameBook = async (bookName) => {\n    try {\n        const newName = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'prompt',\n            title: '重命名世界书',\n            text: `为 \"${bookName}\" 输入新的名称:`,\n            value: bookName,\n        });\n        if (typeof newName === 'string' && newName.trim() && newName !== bookName) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.renameLorebook)(bookName, newName.trim());\n        }\n    }\n    catch (error) {\n        // 用户取消了输入\n        console.log('Rename operation cancelled.');\n    }\n};\n/**\n * 处理删除世界书的逻辑。\n * @param bookName 要删除的世界书的名称\n */\nconst handleDeleteBook = async (bookName) => {\n    try {\n        const confirmation = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认删除',\n            text: `你确定要永久删除世界书 \"${bookName}\" 吗？此操作无法撤销。`,\n        });\n        if (confirmation) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.deleteLorebook)(bookName);\n        }\n    }\n    catch (error) {\n        // 用户取消了确认\n        console.log('Delete operation cancelled.');\n    }\n};\n/**\n * 处理创建新条目的逻辑。\n * @param bookName 新条目所属的世界书名称\n */\nconst handleCreateEntry = async (bookName) => {\n    try {\n        const newEntryData = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showEntryEditorModal)({\n            entry: { keys: [], content: '', comment: '' },\n            bookName,\n            isCreating: true,\n        });\n        // showEntryEditorModal 返回的 newEntryData 不包含 enabled 状态, 我们需要设置默认值\n        const entryToCreate = {\n            ...newEntryData,\n            enabled: true, // 新条目默认启用\n        };\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.createLorebookEntry)(bookName, entryToCreate);\n    }\n    catch (error) {\n        console.log('Create entry operation cancelled.');\n    }\n};\n/**\n * 处理编辑现有条目的逻辑。\n * @param bookName 条目所属的世界书名称\n * @param uid 要编辑的条目的UID\n */\nconst handleEditEntry = async (bookName, uid) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const entry = state.lorebookEntries.get(bookName)?.find(e => e.uid === uid);\n    if (!entry) {\n        console.error(`Entry with UID ${uid} not found in book ${bookName}.`);\n        return;\n    }\n    try {\n        const updatedEntryData = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showEntryEditorModal)({\n            entry: { ...entry }, // 传入副本以防意外修改\n            bookName,\n            isCreating: false,\n        });\n        // 我们只需要更新发生变化的部分\n        const updates = {\n            comment: updatedEntryData.comment,\n            keys: updatedEntryData.keys,\n            content: updatedEntryData.content,\n        };\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateLorebookEntry)(bookName, uid, updates);\n    }\n    catch (error) {\n        console.log('Edit entry operation cancelled.');\n    }\n};\n/**\n * 处理删除条目的逻辑。\n * @param bookName 条目所属的世界书名称\n * @param uid 要删除的条目的UID\n */\nconst handleDeleteEntry = async (bookName, uid) => {\n    try {\n        const confirmation = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认删除',\n            text: `你确定要永久删除这个条目吗？`,\n        });\n        if (confirmation) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.deleteLorebookEntry)(bookName, uid);\n        }\n    }\n    catch (error) {\n        console.log('Delete entry operation cancelled.');\n    }\n};\n/**\n * 处理创建新世界书的逻辑。\n */\nconst handleCreateBook = async () => {\n    try {\n        const newName = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'prompt',\n            title: '创建新世界书',\n            text: '请输入新世界书的名称:',\n            value: 'New-Lorebook',\n        });\n        if (typeof newName === 'string' && newName.trim()) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.createLorebook)(newName.trim());\n        }\n    }\n    catch (error) {\n        console.log('Create lorebook operation cancelled.');\n    }\n};\n// --- 正则表达式事件处理器 ---\nconst handleCreateRegex = async (scope) => {\n    try {\n        const newRegexData = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showRegexEditorModal)({\n            regex: { script_name: '新正则', find_regex: '', replace_string: '' },\n            isCreating: true,\n        });\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.createRegex)({ ...newRegexData, scope });\n    }\n    catch (error) {\n        console.log('Create regex operation cancelled.');\n    }\n};\nconst handleEditRegex = async (regexId) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const allRegexes = [...state.regexes.global, ...state.regexes.character];\n    const regex = allRegexes.find(r => r.id === regexId);\n    if (!regex) {\n        console.error(`Regex with ID ${regexId} not found.`);\n        return;\n    }\n    // 卡片内正则不可编辑\n    if (regex.source === 'card') {\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({ type: 'alert', title: '操作无效', text: '无法编辑来自角色卡的正则表达式。' });\n        return;\n    }\n    try {\n        const updatedRegexData = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showRegexEditorModal)({\n            regex: { ...regex },\n            isCreating: false,\n        });\n        await (0,_core__WEBPACK_IMPORTED_MODULE_1__.updateRegex)(regexId, updatedRegexData);\n    }\n    catch (error) {\n        console.log('Edit regex operation cancelled.');\n    }\n};\nconst handleDeleteRegex = async (regexId) => {\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_2__.getState)();\n    const allRegexes = [...state.regexes.global, ...state.regexes.character];\n    const regex = allRegexes.find(r => r.id === regexId);\n    if (regex && regex.source === 'card') {\n        await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({ type: 'alert', title: '操作无效', text: '无法删除来自角色卡的正则表达式。' });\n        return;\n    }\n    try {\n        const confirmation = await (0,_ui_modals__WEBPACK_IMPORTED_MODULE_3__.showModal)({\n            type: 'confirm',\n            title: '确认删除',\n            text: '你确定要永久删除这个正则表达式吗？',\n        });\n        if (confirmation) {\n            await (0,_core__WEBPACK_IMPORTED_MODULE_1__.deleteRegex)(regexId);\n        }\n    }\n    catch (error) {\n        console.log('Delete regex operation cancelled.');\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/events.ts\n\n}");
  },
  "./src/world_info_optimizer/index.ts": 
  /*!*******************************************!*\
  !*** ./src/world_info_optimizer/index.ts ***!
  \*******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony import */ var _events__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./events */ \"./src/world_info_optimizer/events.ts\");\n/* harmony import */ var _ui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ui */ \"./src/world_info_optimizer/ui.ts\");\n// src/world_info_optimizer/index.ts\n\n\n/**\n * 等待SillyTavern的核心DOM和API准备就绪。\n * @param callback 准备就绪后执行的回调函数\n */\nfunction onReady(callback) {\n    const domSelector = '#extensionsMenu';\n    const maxRetries = 100; // 等待最多约20秒\n    let retries = 0;\n    console.log('[WIO] Starting readiness check...');\n    const interval = setInterval(() => {\n        const parentWin = window.parent;\n        if (!parentWin) {\n            retries++;\n            return;\n        }\n        const domReady = parentWin.document.querySelector(domSelector) !== null;\n        const apiReady = parentWin.TavernHelper && typeof parentWin.TavernHelper.getCharData === 'function' && parentWin.jQuery;\n        if (domReady && apiReady) {\n            clearInterval(interval);\n            console.log('[WIO] SUCCESS: DOM and Core APIs are ready. Initializing script.');\n            try {\n                callback(parentWin);\n            }\n            catch (e) {\n                console.error('[WIO] FATAL: Error during main callback execution.', e);\n            }\n        }\n        else {\n            retries++;\n            if (retries > maxRetries) {\n                clearInterval(interval);\n                console.error('[WIO] FATAL: Readiness check timed out.');\n                if (!domReady)\n                    console.error(`[WIO] -> Failure: DOM element \"${domSelector}\" not found.`);\n                if (!apiReady)\n                    console.error(`[WIO] -> Failure: Core APIs not available. TavernHelper: ${!!parentWin.TavernHelper}, jQuery: ${!!parentWin.jQuery}`);\n            }\n        }\n    }, 200);\n}\n/**\n * 应用程序的主函数。\n * @param parentWindow 父窗口对象\n */\nfunction main(parentWindow) {\n    console.log('[WIO] Initializing World Info Optimizer...');\n    // 1. 注入UI元素到DOM中\n    (0,_ui__WEBPACK_IMPORTED_MODULE_1__.injectUI)(parentWindow);\n    // 2. 初始化UI模块，使其订阅状态变化\n    (0,_ui__WEBPACK_IMPORTED_MODULE_1__.initUI)();\n    // 3. 绑定所有事件处理器\n    (0,_events__WEBPACK_IMPORTED_MODULE_0__.initializeEventHandlers)(parentWindow);\n    console.log('[WIO] World Info Optimizer initialized successfully.');\n}\n// --- 脚本启动 ---\n(() => {\n    // 使用IIFE封装，避免全局污染\n    console.log('[WIO Script] Execution started.');\n    onReady(main);\n})();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiLi9zcmMvd29ybGRfaW5mb19vcHRpbWl6ZXIvaW5kZXgudHMiLCJtYXBwaW5ncyI6Ijs7O0FBQUEsb0NBQW9DO0FBRWU7QUFDWDtBQUV4Qzs7O0dBR0c7QUFDSCxTQUFTLE9BQU8sQ0FBQyxRQUF3QztJQUN2RCxNQUFNLFdBQVcsR0FBRyxpQkFBaUIsQ0FBQztJQUN0QyxNQUFNLFVBQVUsR0FBRyxHQUFHLENBQUMsQ0FBQyxXQUFXO0lBQ25DLElBQUksT0FBTyxHQUFHLENBQUMsQ0FBQztJQUVoQixPQUFPLENBQUMsR0FBRyxDQUFDLG1DQUFtQyxDQUFDLENBQUM7SUFFakQsTUFBTSxRQUFRLEdBQUcsV0FBVyxDQUFDLEdBQUcsRUFBRTtRQUNoQyxNQUFNLFNBQVMsR0FBRyxNQUFNLENBQUMsTUFBTSxDQUFDO1FBQ2hDLElBQUksQ0FBQyxTQUFTLEVBQUUsQ0FBQztZQUNmLE9BQU8sRUFBRSxDQUFDO1lBQ1YsT0FBTztRQUNULENBQUM7UUFFRCxNQUFNLFFBQVEsR0FBRyxTQUFTLENBQUMsUUFBUSxDQUFDLGFBQWEsQ0FBQyxXQUFXLENBQUMsS0FBSyxJQUFJLENBQUM7UUFDeEUsTUFBTSxRQUFRLEdBQ1osU0FBUyxDQUFDLFlBQVksSUFBSSxPQUFPLFNBQVMsQ0FBQyxZQUFZLENBQUMsV0FBVyxLQUFLLFVBQVUsSUFBSSxTQUFTLENBQUMsTUFBTSxDQUFDO1FBRXpHLElBQUksUUFBUSxJQUFJLFFBQVEsRUFBRSxDQUFDO1lBQ3pCLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQztZQUN4QixPQUFPLENBQUMsR0FBRyxDQUFDLGtFQUFrRSxDQUFDLENBQUM7WUFDaEYsSUFBSSxDQUFDO2dCQUNILFFBQVEsQ0FBQyxTQUFTLENBQUMsQ0FBQztZQUN0QixDQUFDO1lBQUMsT0FBTyxDQUFDLEVBQUUsQ0FBQztnQkFDWCxPQUFPLENBQUMsS0FBSyxDQUFDLG9EQUFvRCxFQUFFLENBQUMsQ0FBQyxDQUFDO1lBQ3pFLENBQUM7UUFDSCxDQUFDO2FBQU0sQ0FBQztZQUNOLE9BQU8sRUFBRSxDQUFDO1lBQ1YsSUFBSSxPQUFPLEdBQUcsVUFBVSxFQUFFLENBQUM7Z0JBQ3pCLGFBQWEsQ0FBQyxRQUFRLENBQUMsQ0FBQztnQkFDeEIsT0FBTyxDQUFDLEtBQUssQ0FBQyx5Q0FBeUMsQ0FBQyxDQUFDO2dCQUN6RCxJQUFJLENBQUMsUUFBUTtvQkFBRSxPQUFPLENBQUMsS0FBSyxDQUFDLGtDQUFrQyxXQUFXLGNBQWMsQ0FBQyxDQUFDO2dCQUMxRixJQUFJLENBQUMsUUFBUTtvQkFDWCxPQUFPLENBQUMsS0FBSyxDQUNYLDREQUE0RCxDQUFDLENBQUMsU0FBUyxDQUFDLFlBQVksYUFBYSxDQUFDLENBQUMsU0FBUyxDQUFDLE1BQU0sRUFBRSxDQUN0SCxDQUFDO1lBQ04sQ0FBQztRQUNILENBQUM7SUFDSCxDQUFDLEVBQUUsR0FBRyxDQUFDLENBQUM7QUFDVixDQUFDO0FBRUQ7OztHQUdHO0FBQ0gsU0FBUyxJQUFJLENBQUMsWUFBb0I7SUFDaEMsT0FBTyxDQUFDLEdBQUcsQ0FBQyw0Q0FBNEMsQ0FBQyxDQUFDO0lBRTFELGlCQUFpQjtJQUNqQiw2Q0FBUSxDQUFDLFlBQVksQ0FBQyxDQUFDO0lBRXZCLHNCQUFzQjtJQUN0QiwyQ0FBTSxFQUFFLENBQUM7SUFFVCxlQUFlO0lBQ2YsZ0VBQXVCLENBQUMsWUFBWSxDQUFDLENBQUM7SUFFdEMsT0FBTyxDQUFDLEdBQUcsQ0FBQyxzREFBc0QsQ0FBQyxDQUFDO0FBQ3RFLENBQUM7QUFFRCxlQUFlO0FBQ2YsQ0FBQyxHQUFHLEVBQUU7SUFDSixrQkFBa0I7SUFDbEIsT0FBTyxDQUFDLEdBQUcsQ0FBQyxpQ0FBaUMsQ0FBQyxDQUFDO0lBQy9DLE9BQU8sQ0FBQyxJQUFJLENBQUMsQ0FBQztBQUNoQixDQUFDLENBQUMsRUFBRSxDQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vdGF2ZXJuX2hlbHBlcl90ZW1wbGF0ZS8uL3NyYy93b3JsZF9pbmZvX29wdGltaXplci9pbmRleC50cz8iXSwic291cmNlc0NvbnRlbnQiOlsiLy8gc3JjL3dvcmxkX2luZm9fb3B0aW1pemVyL2luZGV4LnRzXG5cbmltcG9ydCB7IGluaXRpYWxpemVFdmVudEhhbmRsZXJzIH0gZnJvbSAnLi9ldmVudHMnO1xuaW1wb3J0IHsgaW5pdFVJLCBpbmplY3RVSSB9IGZyb20gJy4vdWknO1xuXG4vKipcbiAqIOetieW+hVNpbGx5VGF2ZXJu55qE5qC45b+DRE9N5ZKMQVBJ5YeG5aSH5bCx57uq44CCXG4gKiBAcGFyYW0gY2FsbGJhY2sg5YeG5aSH5bCx57uq5ZCO5omn6KGM55qE5Zue6LCD5Ye95pWwXG4gKi9cbmZ1bmN0aW9uIG9uUmVhZHkoY2FsbGJhY2s6IChwYXJlbnRXaW5kb3c6IFdpbmRvdykgPT4gdm9pZCkge1xuICBjb25zdCBkb21TZWxlY3RvciA9ICcjZXh0ZW5zaW9uc01lbnUnO1xuICBjb25zdCBtYXhSZXRyaWVzID0gMTAwOyAvLyDnrYnlvoXmnIDlpJrnuqYyMOenklxuICBsZXQgcmV0cmllcyA9IDA7XG5cbiAgY29uc29sZS5sb2coJ1tXSU9dIFN0YXJ0aW5nIHJlYWRpbmVzcyBjaGVjay4uLicpO1xuXG4gIGNvbnN0IGludGVydmFsID0gc2V0SW50ZXJ2YWwoKCkgPT4ge1xuICAgIGNvbnN0IHBhcmVudFdpbiA9IHdpbmRvdy5wYXJlbnQ7XG4gICAgaWYgKCFwYXJlbnRXaW4pIHtcbiAgICAgIHJldHJpZXMrKztcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICBjb25zdCBkb21SZWFkeSA9IHBhcmVudFdpbi5kb2N1bWVudC5xdWVyeVNlbGVjdG9yKGRvbVNlbGVjdG9yKSAhPT0gbnVsbDtcbiAgICBjb25zdCBhcGlSZWFkeSA9XG4gICAgICBwYXJlbnRXaW4uVGF2ZXJuSGVscGVyICYmIHR5cGVvZiBwYXJlbnRXaW4uVGF2ZXJuSGVscGVyLmdldENoYXJEYXRhID09PSAnZnVuY3Rpb24nICYmIHBhcmVudFdpbi5qUXVlcnk7XG5cbiAgICBpZiAoZG9tUmVhZHkgJiYgYXBpUmVhZHkpIHtcbiAgICAgIGNsZWFySW50ZXJ2YWwoaW50ZXJ2YWwpO1xuICAgICAgY29uc29sZS5sb2coJ1tXSU9dIFNVQ0NFU1M6IERPTSBhbmQgQ29yZSBBUElzIGFyZSByZWFkeS4gSW5pdGlhbGl6aW5nIHNjcmlwdC4nKTtcbiAgICAgIHRyeSB7XG4gICAgICAgIGNhbGxiYWNrKHBhcmVudFdpbik7XG4gICAgICB9IGNhdGNoIChlKSB7XG4gICAgICAgIGNvbnNvbGUuZXJyb3IoJ1tXSU9dIEZBVEFMOiBFcnJvciBkdXJpbmcgbWFpbiBjYWxsYmFjayBleGVjdXRpb24uJywgZSk7XG4gICAgICB9XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHJpZXMrKztcbiAgICAgIGlmIChyZXRyaWVzID4gbWF4UmV0cmllcykge1xuICAgICAgICBjbGVhckludGVydmFsKGludGVydmFsKTtcbiAgICAgICAgY29uc29sZS5lcnJvcignW1dJT10gRkFUQUw6IFJlYWRpbmVzcyBjaGVjayB0aW1lZCBvdXQuJyk7XG4gICAgICAgIGlmICghZG9tUmVhZHkpIGNvbnNvbGUuZXJyb3IoYFtXSU9dIC0+IEZhaWx1cmU6IERPTSBlbGVtZW50IFwiJHtkb21TZWxlY3Rvcn1cIiBub3QgZm91bmQuYCk7XG4gICAgICAgIGlmICghYXBpUmVhZHkpXG4gICAgICAgICAgY29uc29sZS5lcnJvcihcbiAgICAgICAgICAgIGBbV0lPXSAtPiBGYWlsdXJlOiBDb3JlIEFQSXMgbm90IGF2YWlsYWJsZS4gVGF2ZXJuSGVscGVyOiAkeyEhcGFyZW50V2luLlRhdmVybkhlbHBlcn0sIGpRdWVyeTogJHshIXBhcmVudFdpbi5qUXVlcnl9YCxcbiAgICAgICAgICApO1xuICAgICAgfVxuICAgIH1cbiAgfSwgMjAwKTtcbn1cblxuLyoqXG4gKiDlupTnlKjnqIvluo/nmoTkuLvlh73mlbDjgIJcbiAqIEBwYXJhbSBwYXJlbnRXaW5kb3cg54i256qX5Y+j5a+56LGhXG4gKi9cbmZ1bmN0aW9uIG1haW4ocGFyZW50V2luZG93OiBXaW5kb3cpIHtcbiAgY29uc29sZS5sb2coJ1tXSU9dIEluaXRpYWxpemluZyBXb3JsZCBJbmZvIE9wdGltaXplci4uLicpO1xuXG4gIC8vIDEuIOazqOWFpVVJ5YWD57Sg5YiwRE9N5LitXG4gIGluamVjdFVJKHBhcmVudFdpbmRvdyk7XG5cbiAgLy8gMi4g5Yid5aeL5YyWVUnmqKHlnZfvvIzkvb/lhbborqLpmIXnirbmgIHlj5jljJZcbiAgaW5pdFVJKCk7XG5cbiAgLy8gMy4g57uR5a6a5omA5pyJ5LqL5Lu25aSE55CG5ZmoXG4gIGluaXRpYWxpemVFdmVudEhhbmRsZXJzKHBhcmVudFdpbmRvdyk7XG5cbiAgY29uc29sZS5sb2coJ1tXSU9dIFdvcmxkIEluZm8gT3B0aW1pemVyIGluaXRpYWxpemVkIHN1Y2Nlc3NmdWxseS4nKTtcbn1cblxuLy8gLS0tIOiEmuacrOWQr+WKqCAtLS1cbigoKSA9PiB7XG4gIC8vIOS9v+eUqElJRkXlsIHoo4XvvIzpgb/lhY3lhajlsYDmsaHmn5NcbiAgY29uc29sZS5sb2coJ1tXSU8gU2NyaXB0XSBFeGVjdXRpb24gc3RhcnRlZC4nKTtcbiAgb25SZWFkeShtYWluKTtcbn0pKCk7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/index.ts\n\n}");
  },
  "./src/world_info_optimizer/store.ts": 
  /*!*******************************************!*\
  !*** ./src/world_info_optimizer/store.ts ***!
  \*******************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getState: () => (/* binding */ getState),\n/* harmony export */   resetState: () => (/* binding */ resetState),\n/* harmony export */   setActiveTab: () => (/* binding */ setActiveTab),\n/* harmony export */   setAllData: () => (/* binding */ setAllData),\n/* harmony export */   setDataLoaded: () => (/* binding */ setDataLoaded),\n/* harmony export */   setLoadError: () => (/* binding */ setLoadError),\n/* harmony export */   setLoading: () => (/* binding */ setLoading),\n/* harmony export */   setSearchQuery: () => (/* binding */ setSearchQuery),\n/* harmony export */   subscribe: () => (/* binding */ subscribe),\n/* harmony export */   updateState: () => (/* binding */ updateState)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ \"./src/world_info_optimizer/constants.ts\");\n// src/world_info_optimizer/store.ts\n\n// --- State Definition ---\nconst initialState = {\n    regexes: { global: [], character: [] },\n    lorebooks: { character: [] },\n    chatLorebook: null,\n    allLorebooks: [],\n    lorebookEntries: new Map(),\n    lorebookUsage: new Map(),\n    activeTab: _constants__WEBPACK_IMPORTED_MODULE_0__.INITIAL_ACTIVE_TAB,\n    isDataLoaded: false,\n    isLoading: false,\n    loadError: null,\n    searchFilters: {\n        bookName: true,\n        entryName: true,\n        keywords: true,\n        content: true,\n    },\n    searchQuery: '',\n    multiSelectMode: false,\n    selectedItems: new Set(),\n};\n// 使用深拷贝来创建可变状态，避免直接修改initialState\nlet state = JSON.parse(JSON.stringify(initialState));\n// Map和Set不能通过JSON.stringify/parse正确克隆，需要手动重新创建\nstate.lorebookEntries = new Map();\nstate.lorebookUsage = new Map();\nstate.selectedItems = new Set();\nstate.isLoading = false;\nstate.loadError = null;\nconst listeners = [];\n/**\n * 订阅状态变化。\n * @param listener 当状态更新时要调用的回调函数。\n * @returns 一个取消订阅的函数。\n */\nconst subscribe = (listener) => {\n    listeners.push(listener);\n    return () => {\n        const index = listeners.indexOf(listener);\n        if (index > -1) {\n            listeners.splice(index, 1);\n        }\n    };\n};\n/**\n * 通知所有监听器状态已更新。\n */\nconst notify = () => {\n    // 传递状态的深拷贝，以防监听器意外修改状态\n    const deepCopiedState = {\n        ...state,\n        // 深拷贝 Map，包括其内部的数组\n        lorebookEntries: new Map(Array.from(state.lorebookEntries.entries()).map(([key, value]) => [\n            key,\n            [...value], // 复制数组\n        ])),\n        // 深拷贝 Map，包括其内部的数组\n        lorebookUsage: new Map(Array.from(state.lorebookUsage.entries()).map(([key, value]) => [\n            key,\n            [...value], // 复制数组\n        ])),\n        selectedItems: new Set(state.selectedItems),\n        // 深拷贝嵌套对象\n        regexes: {\n            global: [...state.regexes.global],\n            character: [...state.regexes.character],\n        },\n        lorebooks: {\n            character: [...state.lorebooks.character],\n        },\n        allLorebooks: [...state.allLorebooks],\n        searchFilters: { ...state.searchFilters },\n    };\n    listeners.forEach(listener => listener(deepCopiedState));\n};\n// --- State Accessors and Mutators ---\n/**\n * 获取当前状态对象的快照。\n * @returns 当前应用状态的深拷贝。\n */\nconst getState = () => {\n    // 返回深拷贝以保证状态的不可变性\n    return {\n        ...state,\n        // 深拷贝 Map，包括其内部的数组\n        lorebookEntries: new Map(Array.from(state.lorebookEntries.entries()).map(([key, value]) => [\n            key,\n            [...value], // 复制数组\n        ])),\n        // 深拷贝 Map，包括其内部的数组\n        lorebookUsage: new Map(Array.from(state.lorebookUsage.entries()).map(([key, value]) => [\n            key,\n            [...value], // 复制数组\n        ])),\n        selectedItems: new Set(state.selectedItems),\n        // 深拷贝嵌套对象\n        regexes: {\n            global: [...state.regexes.global],\n            character: [...state.regexes.character],\n        },\n        lorebooks: {\n            character: [...state.lorebooks.character],\n        },\n        allLorebooks: [...state.allLorebooks],\n        searchFilters: { ...state.searchFilters },\n    };\n};\n/**\n * 更新应用状态并通知所有订阅者。\n * @param updater 一个函数，接收当前状态并返回一个新的（或修改过的）状态部分。\n */\nconst updateState = (updater) => {\n    const updates = updater(state);\n    state = { ...state, ...updates };\n    notify();\n};\n/**\n * 重置整个应用状态到初始值。\n */\nconst resetState = () => {\n    state = JSON.parse(JSON.stringify(initialState));\n    state.lorebookEntries = new Map();\n    state.lorebookUsage = new Map();\n    state.selectedItems = new Set();\n    notify();\n};\n// --- Specific State Updater Functions ---\nconst setActiveTab = (tabId) => {\n    updateState(s => ({ ...s, activeTab: tabId }));\n};\nconst setDataLoaded = (isLoaded) => {\n    updateState(s => ({ ...s, isDataLoaded: isLoaded }));\n};\nconst setLoading = (isLoading) => {\n    updateState(s => ({ ...s, isLoading }));\n};\nconst setLoadError = (error) => {\n    updateState(s => ({ ...s, loadError: error }));\n};\nconst setSearchQuery = (query) => {\n    updateState(s => ({ ...s, searchQuery: query }));\n};\nconst setAllData = (data) => {\n    updateState(s => ({\n        ...s,\n        regexes: { global: data.globalRegexes, character: data.characterRegexes },\n        allLorebooks: data.allLorebooks,\n        lorebooks: { character: data.characterLorebooks },\n        chatLorebook: data.chatLorebook,\n        lorebookEntries: data.lorebookEntries,\n        lorebookUsage: data.lorebookUsage,\n        isDataLoaded: true,\n    }));\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/store.ts\n\n}");
  },
  "./src/world_info_optimizer/ui.ts": 
  /*!****************************************!*\
  !*** ./src/world_info_optimizer/ui.ts ***!
  \****************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   initUI: () => (/* binding */ initUI),\n/* harmony export */   injectUI: () => (/* binding */ injectUI)\n/* harmony export */ });\n/* harmony import */ var _constants__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./constants */ "./src/world_info_optimizer/constants.ts");\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./store */ "./src/world_info_optimizer/store.ts");\n/* harmony import */ var _ui_views__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ui/views */ "./src/world_info_optimizer/ui/views.ts");\n// src/world_info_optimizer/ui.ts\n\n\n\n// --- Private Variables ---\nlet parentDoc;\nlet $;\n// --- Main Panel and Button Injection ---\nconst injectUI = (parentWindow) => {\n    parentDoc = parentWindow.document;\n    $ = parentWindow.jQuery;\n    if ($(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}`, parentDoc).length > 0) {\n        console.log(\'[WIO] UI already injected.\');\n        return;\n    }\n    const styles = `\n        :root {\n            /* 紧凑布局 */\n            --wio-font-size-sm: 11px;\n            --wio-font-size-md: 13px;\n            --wio-font-size-lg: 15px;\n            --wio-spacing-xs: 2px;\n            --wio-spacing-sm: 6px;\n            --wio-spacing-md: 10px;\n            --wio-spacing-lg: 14px;\n\n            /* 扁平化风格 */\n            --wio-border-radius: 4px;\n            --wio-shadow: none;\n            \n            /* 暗色主题 */\n            --wio-bg-primary: #1f1f1f;\n            --wio-bg-secondary: #2d2d2d;\n            --wio-bg-tertiary: #3c3c3c;\n            --wio-bg-toolbar: #252525;\n            --wio-text-primary: #e0e0e0;\n            --wio-text-secondary: #9e9e9e;\n            --wio-highlight-color: #29b6f6;\n            --wio-border-color: #424242;\n        }\n\n        /* All WIO styles here... */\n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID} {\n            display: none;\n            position: fixed;\n            top: 0;\n            left: 0;\n            width: 100vw;\n            height: 100vh;\n            z-index: 10000;\n            overflow: hidden;\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID} .wio-panel-inner {\n            display: flex;\n            flex-direction: column;\n            height: 100vh;\n            width: 100vw;\n            background-color: var(--wio-bg-primary);\n            color: var(--wio-text-primary);\n        }\n\n\n        /* 头部样式 */\n        .wio-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-secondary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        .wio-header h2 {\n            margin: 0;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-close-btn {\n            background: none;\n            border: none;\n            color: var(--wio-text-secondary);\n            font-size: 24px;\n            cursor: pointer;\n            padding: var(--wio-spacing-xs);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            transition: color 0.2s ease;\n        }\n        \n        .wio-close-btn:hover {\n            color: var(--wio-text-primary);\n        }\n        \n        /* 选项卡样式 */\n        .wio-tabs {\n            display: flex;\n            background-color: var(--wio-bg-tertiary);\n            overflow-x: auto;\n            white-space: nowrap;\n            flex-shrink: 0;\n            border-bottom: 1px solid var(--wio-border-color);\n            -ms-overflow-style: none;\n            scrollbar-width: none;\n        }\n        \n        .wio-tabs::-webkit-scrollbar {\n            display: none;\n        }\n        \n        .wio-tab-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: none;\n            background-color: transparent;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            border-bottom: 2px solid transparent;\n            flex-shrink: 0;\n            font-size: var(--wio-font-size-md);\n            transition: all 0.2s ease;\n            outline: none;\n        }\n        \n        .wio-tab-btn.active {\n            color: var(--wio-highlight-color);\n            border-bottom-color: var(--wio-highlight-color);\n            background-color: rgba(41, 182, 246, 0.15);\n        }\n        \n        .wio-tab-btn:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n        \n        /* 工具栏样式 */\n        .wio-toolbar {\n            padding: var(--wio-spacing-md);\n            display: flex;\n            gap: var(--wio-spacing-md);\n            background-color: var(--wio-bg-toolbar);\n            border-bottom: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID} {\n            flex-grow: 1;\n            padding: var(--wio-spacing-md);\n            border: 1px solid var(--wio-border-color);\n            border-radius: var(--wio-border-radius);\n            background-color: var(--wio-bg-secondary); /* Darker background */\n            color: var(--wio-text-primary);\n            font-size: var(--wio-font-size-md);\n            outline: none;\n            transition: border-color 0.2s ease, box-shadow 0.2s ease;\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}::placeholder {\n            color: var(--wio-text-secondary);\n        }\n        \n        #${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}:focus {\n            border-color: var(--wio-highlight-color);\n            box-shadow: none;\n        }\n        \n        .wio-toolbar button {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            border: none;\n            border-radius: var(--wio-border-radius);\n            background-color: transparent;\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            gap: var(--wio-spacing-xs);\n            transition: all 0.2s ease;\n            outline: none;\n            min-width: 40px;\n            min-height: 40px;\n        }\n        \n        .wio-toolbar button:hover {\n            background-color: var(--wio-bg-tertiary);\n            color: var(--wio-text-primary);\n        }\n        \n        .wio-toolbar button:focus {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 主内容区域样式 */\n        .wio-main-content {\n            flex-grow: 1;\n            overflow-y: auto;\n            padding: var(--wio-spacing-lg);\n        }\n        \n        /* 列表样式 */\n        .wio-book-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n            background-color: var(--wio-bg-secondary);\n        }\n        \n        .wio-book-header {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-tertiary);\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n        }\n        \n        .wio-book-header h4 {\n            margin: 0;\n            flex-grow: 1;\n            font-size: var(--wio-font-size-lg);\n        }\n        \n        .wio-usage-pill {\n            padding: 2px 8px;\n            background-color: var(--wio-highlight-color);\n            color: #1f1f1f;\n            border-radius: 12px;\n            font-size: var(--wio-font-size-sm);\n            font-weight: bold;\n        }\n        \n        .wio-item-controls {\n            display: flex;\n            gap: var(--wio-spacing-xs);\n        }\n        \n        .wio-item-controls button {\n            padding: var(--wio-spacing-xs);\n            background-color: transparent;\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: var(--wio-text-secondary);\n            cursor: pointer;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            transition: all 0.2s ease;\n        }\n        \n        .wio-item-controls button:hover {\n            background-color: var(--wio-bg-tertiary);\n            color: var(--wio-text-primary);\n        }\n        \n        .wio-entry-list {\n            background-color: var(--wio-bg-secondary);\n        }\n        \n        .wio-entry-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-bg-tertiary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-entry-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-entry-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n        }\n        \n        .wio-entry-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-entry-keys {\n            font-size: var(--wio-font-size-sm);\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            flex-grow: 1;\n            word-break: break-word;\n        }\n        \n        .wio-entry-actions,\n        .wio-regex-actions {\n            padding: var(--wio-spacing-md);\n            text-align: center;\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-create-entry-btn,\n        .wio-create-regex-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: var(--wio-highlight-color);\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: #ffffff;\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            transition: background-color 0.2s ease;\n        }\n\n        .wio-create-entry-btn:hover,\n        .wio-create-regex-btn:hover {\n            background-color: #0091ea; /* A slightly darker shade of the highlight color */\n        }\n\n        /* 正则表达式样式 */\n        .wio-regex-group {\n            margin-bottom: var(--wio-spacing-lg);\n            border-radius: var(--wio-border-radius);\n            overflow: hidden;\n            background-color: var(--wio-bg-secondary);\n        }\n        \n        .wio-regex-group h3 {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            margin: 0;\n            background-color: var(--wio-bg-tertiary);\n        }\n        \n        .wio-regex-item {\n            padding: var(--wio-spacing-md);\n            border-bottom: 1px solid var(--wio-bg-tertiary);\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n        }\n        \n        .wio-regex-item:last-child {\n            border-bottom: none;\n        }\n        \n        .wio-regex-main {\n            display: flex;\n            align-items: center;\n            gap: var(--wio-spacing-md);\n            flex-grow: 1;\n            flex-wrap: wrap;\n        }\n        \n        .wio-regex-name {\n            font-weight: bold;\n            flex-shrink: 0;\n        }\n        \n        .wio-regex-find,\n        .wio-regex-replace {\n            background-color: var(--wio-bg-primary);\n            padding: 4px 8px;\n            border-radius: 4px;\n            font-family: monospace;\n            font-size: var(--wio-font-size-sm);\n            word-break: break-all;\n            border: 1px solid var(--wio-border-color);\n        }\n        \n        .wio-info-text {\n            text-align: center;\n            color: var(--wio-text-secondary);\n            font-style: italic;\n            padding: var(--wio-spacing-lg);\n        }\n\n        .wio-error-container {\n            text-align: center;\n            padding: var(--wio-spacing-lg);\n        }\n\n        .wio-error-text {\n            color: #ff6b6b;\n            margin-bottom: var(--wio-spacing-md);\n            font-weight: bold;\n        }\n\n        .wio-retry-btn {\n            padding: var(--wio-spacing-md) var(--wio-spacing-lg);\n            background-color: #c62828;\n            border: none;\n            border-radius: var(--wio-border-radius);\n            color: #ffffff;\n            cursor: pointer;\n            font-size: var(--wio-font-size-md);\n            transition: background-color 0.2s ease;\n        }\n\n        .wio-retry-btn:hover {\n            background-color: #b71c1c;\n        }\n        \n        .wio-search-highlight {\n            background-color: rgba(255, 255, 0, 0.3);\n            padding: 0 2px;\n            border-radius: 2px;\n        }\n\n        /* 页脚样式 */\n        .wio-footer {\n            padding: var(--wio-spacing-sm) var(--wio-spacing-lg);\n            background-color: var(--wio-bg-tertiary);\n            font-size: var(--wio-font-size-sm);\n            text-align: right;\n<![CDATA[\n        /* SweetAlert2 输入框样式覆盖 */\n        .swal2-input, .swal2-textarea {\n            background-color: var(--wio-bg-secondary) !important;\n            color: var(--wio-text-primary) !important;\n            border: 1px solid var(--wio-border-color) !important;\n        }\n        \n        .swal2-input:focus, .swal2-textarea:focus {\n            border-color: var(--wio-highlight-color) !important;\n            box-shadow: none !important;\n        }\n\n<![CDATA[\n        /* SweetAlert2 模态框样式覆盖 */\n        .swal2-popup {\n            background-color: var(--wio-bg-primary) !important;\n            color: var(--wio-text-primary) !important;\n        }\n\n        .swal2-title {\n            color: var(--wio-text-primary) !important;\n        }\n\n        .swal2-html-container {\n            color: var(--wio-text-secondary) !important;\n        }\n\n        .swal2-confirm, .swal2-cancel {\n            border-radius: var(--wio-border-radius) !important;\n            transition: background-color 0.2s ease;\n        }\n\n        .swal2-confirm {\n            background-color: var(--wio-highlight-color) !important;\n        }\n        \n        .swal2-cancel {\n            background-color: var(--wio-bg-tertiary) !important;\n        }\n        \n        .swal2-toast {\n             background-color: var(--wio-bg-secondary) !important;\n             color: var(--wio-text-primary) !important;\n        }\n\n]]>\n]]>\n            border-top: 1px solid var(--wio-border-color);\n            flex-shrink: 0;\n        }\n\n        /* 复选框样式优化 */\n        input[type="checkbox"] {\n            transform: scale(1.1);\n            accent-color: var(--wio-highlight-color);\n            margin-right: var(--wio-spacing-sm);\n            background-color: var(--wio-bg-secondary);\n            border: 1px solid var(--wio-border-color);\n            border-radius: 2px;\n            appearance: none;\n            -webkit-appearance: none;\n            width: 16px;\n            height: 16px;\n            cursor: pointer;\n            position: relative;\n            top: 2px;\n        }\n\n        input[type="checkbox"]:checked {\n            background-color: var(--wio-highlight-color);\n            border-color: var(--wio-highlight-color);\n        }\n\n        input[type="checkbox"]:checked::after {\n            content: \'✔\';\n            position: absolute;\n            color: #1f1f1f;\n            font-size: 12px;\n            top: -1px;\n            left: 2px;\n        }\n        \n        /* 聚焦样式优化 */\n        :focus-visible {\n            outline: 2px solid var(--wio-highlight-color);\n            outline-offset: 2px;\n        }\n\n        /* 滚动条样式 */\n        .wio-main-content::-webkit-scrollbar,\n        .wio-tabs::-webkit-scrollbar {\n            width: 8px;\n            height: 8px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-track,\n        .wio-tabs::-webkit-scrollbar-track {\n            background: var(--wio-bg-tertiary);\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb,\n        .wio-tabs::-webkit-scrollbar-thumb {\n            background: var(--wio-border-color);\n            border-radius: 4px;\n        }\n        \n        .wio-main-content::-webkit-scrollbar-thumb:hover,\n        .wio-tabs::-webkit-scrollbar-thumb:hover {\n            background: #777;\n        }\n\n        /* 媒体查询：小型设备优化 */\n        @media (max-width: 767px) {\n            :root {\n                --wio-spacing-xs: 2px;\n                --wio-spacing-sm: 6px;\n                --wio-spacing-md: 8px;\n                --wio-spacing-lg: 10px;\n                --wio-font-size-sm: 11px;\n                --wio-font-size-md: 13px;\n                --wio-font-size-lg: 15px;\n            }\n            \n            .wio-header h2 {\n                font-size: var(--wio-font-size-md);\n            }\n            \n            .wio-entry-main {\n                flex-wrap: wrap;\n            }\n            \n            .wio-entry-name {\n                flex-basis: 100%;\n                margin-bottom: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-main {\n                flex-direction: column;\n                align-items: flex-start;\n                gap: var(--wio-spacing-xs);\n            }\n            \n            .wio-regex-name,\n            .wio-regex-find,\n            .wio-regex-replace {\n                width: 100%;\n                box-sizing: border-box;\n            }\n            \n            .wio-toolbar {\n                flex-wrap: wrap;\n                gap: var(--wio-spacing-sm); /* 缩小gap */\n            }\n            \n            #${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID} {\n                flex-basis: 100%; /* 确保搜索框始终占满一行 */\n            }\n            \n            .wio-toolbar button {\n                flex-grow: 1; /* 让按钮平均分配剩余空间 */\n                min-width: 44px; /* 保证最小触摸尺寸 */\n            }\n\n            .wio-tabs {\n                position: relative; /* 为伪元素定位 */\n            }\n\n            .wio-tabs::before,\n            .wio-tabs::after {\n                content: \'\';\n                position: absolute;\n                top: 0;\n                bottom: 0;\n                width: 20px; /* 渐变宽度 */\n                pointer-events: none; /* 允许点击穿透 */\n            }\n\n            .wio-tabs::before {\n                left: 0;\n                background: linear-gradient(to right, var(--wio-bg-tertiary), transparent);\n            }\n\n            .wio-tabs::after {\n                right: 0;\n                background: linear-gradient(to left, var(--wio-bg-tertiary), transparent);\n            }\n\n            .wio-main-content {\n                padding: var(--wio-spacing-md); /* 统一内边距 */\n            }\n\n            .wio-entry-keys {\n                font-size: var(--wio-font-size-sm);\n                white-space: normal; /* 允许长文本换行 */\n                word-break: break-all;\n            }\n            \n            /* 触摸目标优化 */\n            button,\n            input[type="checkbox"] {\n                touch-action: manipulation;\n            }\n        }\n        \n        /* 平板设备优化 */\n        @media (min-width: 768px) and (max-width: 1024px) {\n            #${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID} {\n                width: 90%;\n                height: 80%;\n            }\n        }\n\n        /* 高对比度模式支持 */\n        @media (prefers-contrast: high) {\n            :root {\n                --wio-border-color: #fff !important;\n                --wio-bg-primary: #000 !important;\n                --wio-bg-secondary: #333 !important;\n                --wio-bg-tertiary: #222 !important;\n                --wio-bg-toolbar: #444 !important;\n                --wio-text-primary: #fff !important;\n                --wio-text-secondary: #ddd !important;\n                --wio-highlight-color: #ff0 !important;\n            }\n        }\n\n\n        /* 减少动画模式支持 */\n        @media (prefers-reduced-motion: reduce) {\n            * {\n                animation-duration: 0.01ms !important;\n                animation-iteration-count: 1 !important;\n                transition-duration: 0.01ms !important;\n            }\n        }\n    `;\n    const styleSheet = parentDoc.createElement(\'style\');\n    styleSheet.innerText = styles;\n    parentDoc.head.appendChild(styleSheet);\n    const panelHtml = `\n        <div id="${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}">\n            <div class="wio-panel-inner">\n                <div class="wio-header">\n                    <h2>世界书 & 正则便捷管理 (WIO)</h2>\n                    <button class="wio-close-btn" aria-label="关闭面板">&times;</button>\n                </div>\n                <div class="wio-tabs" role="tablist">\n                    <button class="wio-tab-btn" data-tab-id="global-lore" role="tab" aria-controls="global-lore-content" aria-selected="false">全局世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="char-lore" role="tab" aria-controls="char-lore-content" aria-selected="false">角色世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="chat-lore" role="tab" aria-controls="chat-lore-content" aria-selected="false">聊天世界书</button>\n                    <button class="wio-tab-btn" data-tab-id="global-regex" role="tab" aria-controls="global-regex-content" aria-selected="false">全局正则</button>\n                    <button class="wio-tab-btn" data-tab-id="char-regex" role="tab" aria-controls="char-regex-content" aria-selected="false">角色正则</button>\n                </div>\n                <div class="wio-toolbar">\n                    <input type="search" id="${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}" placeholder="搜索..." aria-label="搜索内容">\n                    <button id="${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}" title="刷新数据" aria-label="刷新数据"><i class="fa-solid fa-sync"></i></button>\n                    <button id="${_constants__WEBPACK_IMPORTED_MODULE_0__.CREATE_LOREBOOK_BTN_ID}" title="新建世界书" aria-label="新建世界书"><i class="fa-solid fa-plus"></i></button>\n                </div>\n                <div class="wio-main-content" role="main" id="tab-content-container"></div>\n                <div class="wio-footer">\n                    <span>WIO v3.0 (Refactored)</span>\n                </div>\n            </div>\n        </div>\n    `;\n    $(\'body\', parentDoc).append(panelHtml);\n    const extensionButton = `\n        <div id="${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ID}" class="list-group-item">\n            <img src="${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_ICON_URL}" style="width: 24px; height: 24px; margin-right: 10px;">\n            <span>${_constants__WEBPACK_IMPORTED_MODULE_0__.BUTTON_TEXT_IN_MENU}</span>\n        </div>\n    `;\n    $(\'#extensionsMenu\', parentDoc).append(extensionButton);\n    console.log(\'[WIO] UI Injected successfully.\');\n};\n// --- Core Render Logic ---\nconst render = () => {\n    if (!parentDoc)\n        return;\n    const state = (0,_store__WEBPACK_IMPORTED_MODULE_1__.getState)();\n    const $panel = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.PANEL_ID}`, parentDoc);\n    if (!$panel.length)\n        return;\n    $panel.find(\'.wio-tab-btn\').removeClass(\'active\');\n    $panel.find(`.wio-tab-btn[data-tab-id="${state.activeTab}"]`).addClass(\'active\');\n    const $mainContent = $panel.find(\'.wio-main-content\');\n    // 处理加载状态\n    if (state.isLoading) {\n        $mainContent.html(\'<p class="wio-info-text">正在加载数据...</p>\');\n        return;\n    }\n    // 处理错误状态\n    if (state.loadError) {\n        $mainContent.html(`\n      <div class="wio-error-container">\n        <p class="wio-error-text">${state.loadError}</p>\n        <button id="${_constants__WEBPACK_IMPORTED_MODULE_0__.REFRESH_BTN_ID}-retry" class="wio-retry-btn">重试</button>\n      </div>\n    `);\n        return;\n    }\n    // 处理未加载状态\n    if (!state.isDataLoaded) {\n        $mainContent.html(\'<p class="wio-info-text">点击刷新按钮加载数据</p>\');\n        return;\n    }\n    const searchTerm = state.searchQuery.toLowerCase();\n    const $searchInput = $(`#${_constants__WEBPACK_IMPORTED_MODULE_0__.SEARCH_INPUT_ID}`, parentDoc);\n    if ($searchInput.val() !== state.searchQuery) {\n        $searchInput.val(state.searchQuery);\n    }\n    switch (state.activeTab) {\n        case \'global-lore\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderGlobalLorebookView)(state, searchTerm));\n            break;\n        case \'char-lore\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderCharacterLorebookView)(state, searchTerm));\n            break;\n        case \'chat-lore\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderChatLorebookView)(state, searchTerm));\n            break;\n        case \'global-regex\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderRegexView)(state.regexes.global, searchTerm, \'全局正则\', \'global\'));\n            break;\n        case \'char-regex\':\n            $mainContent.html((0,_ui_views__WEBPACK_IMPORTED_MODULE_2__.renderRegexView)(state.regexes.character, searchTerm, \'角色正则\', \'character\'));\n            break;\n        default:\n            $mainContent.html(`<p>未知视图: ${state.activeTab}</p>`);\n    }\n};\n// --- UI Initialization ---\nconst initUI = () => {\n    (0,_store__WEBPACK_IMPORTED_MODULE_1__.subscribe)(render);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui.ts\n\n}');
  },
  "./src/world_info_optimizer/ui/elements.ts": 
  /*!*************************************************!*\
  !*** ./src/world_info_optimizer/ui/elements.ts ***!
  \*************************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval('{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEntryElement: () => (/* binding */ createEntryElement),\n/* harmony export */   createLorebookElement: () => (/* binding */ createLorebookElement),\n/* harmony export */   createRegexItemElement: () => (/* binding */ createRegexItemElement)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ "./src/world_info_optimizer/ui/helpers.ts");\n// src/world_info_optimizer/ui/elements.ts\n\nconst createLorebookElement = (book, state, searchTerm, isGlobal = true, entriesToShow) => {\n    const entries = entriesToShow !== undefined ? entriesToShow : state.lorebookEntries.get(book.name) || [];\n    const totalEntries = state.lorebookEntries.get(book.name)?.length || 0;\n    const usage = state.lorebookUsage.get(book.name) || [];\n    const entryCountText = searchTerm && entries.length !== totalEntries ? `${entries.length} / ${totalEntries}` : `${totalEntries}`;\n    return `\n        <div class="wio-book-group" data-book-name="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}">\n            <div class="wio-book-header">\n                ${isGlobal ? `<input type="checkbox" ${book.enabled ? \'checked\' : \'\'} class="wio-global-book-toggle" aria-label="启用 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}">` : \'\'}\n                <h4>${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(book.name, searchTerm)} <span class="wio-entry-count">(${entryCountText})</span></h4>\n                ${usage.length > 0 ? `<span class="wio-usage-pill" title="被 ${usage.join(\', \')} 使用" aria-label="被 ${usage.length} 个角色使用">${usage.length}</span>` : \'\'}\n                <div class="wio-item-controls">\n                    <button class="wio-rename-book-btn" title="重命名" aria-label="重命名 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}"><i class="fa-solid fa-pencil"></i></button>\n                    <button class="wio-delete-book-btn" title="删除" aria-label="删除 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}"><i class="fa-solid fa-trash-can"></i></button>\n                </div>\n            </div>\n            <div class="wio-entry-list">\n                ${entries.map(entry => createEntryElement(entry, book.name, searchTerm)).join(\'\')}\n                <div class="wio-entry-actions"><button class="wio-create-entry-btn" data-book-name="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)}" aria-label="在 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(book.name)} 中新建条目">+ 新建条目</button></div>\n            </div>\n        </div>\n    `;\n};\nconst createEntryElement = (entry, bookName, searchTerm) => {\n    if (!entry)\n        return \'\'; // 代码加固\n    const displayName = entry.comment || \'(未命名条目)\';\n    return `\n        <div class="wio-entry-item" data-uid="${entry.uid}" data-book-name="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(bookName)}">\n            <div class="wio-entry-main">\n                <input type="checkbox" ${entry.enabled ? \'checked\' : \'\'} class="wio-entry-toggle" aria-label="启用 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}">\n                <span class="wio-entry-name">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(displayName, searchTerm)}</span>\n                <span class="wio-entry-keys">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)((entry.keys || []).join(\', \'), searchTerm)}</span>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-entry-btn" title="编辑" aria-label="编辑 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-entry-btn" title="删除" aria-label="删除 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `;\n};\nconst createRegexItemElement = (regex, searchTerm) => {\n    if (!regex)\n        return \'\'; // 代码加固\n    const displayName = regex.script_name || \'(未命名正则)\';\n    const regexId = regex.id || `${regex.scope}-${btoa(unescape(encodeURIComponent(regex.script_name + regex.find_regex)))}`;\n    return `\n        <div class="wio-regex-item" data-id="${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regexId)}" data-scope="${regex.scope}">\n            <div class="wio-regex-main">\n                <input type="checkbox" ${regex.enabled ? \'checked\' : \'\'} class="wio-regex-toggle" aria-label="启用 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}">\n                <span class="wio-regex-name">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(displayName, searchTerm)} ${regex.source === \'card\' ? \'<span class="wio-regex-source-badge" aria-label="卡内正则">(卡)</span>\' : \'\'}</span>\n                <code class="wio-regex-find" title="查找: ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.find_regex)}">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(regex.find_regex, searchTerm)}</code>\n                <i class="fa-solid fa-arrow-right-long" aria-hidden="true"></i>\n                <code class="wio-regex-replace" title="替换为: ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.replace_string)}">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.highlightText)(regex.replace_string, searchTerm)}</code>\n            </div>\n            <div class="wio-item-controls">\n                <button class="wio-edit-regex-btn" title="编辑" aria-label="编辑 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-pencil"></i></button>\n                <button class="wio-delete-regex-btn" title="删除" aria-label="删除 ${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(displayName)}"><i class="fa-solid fa-trash-can"></i></button>\n            </div>\n        </div>\n    `;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/elements.ts\n\n}');
  },
  "./src/world_info_optimizer/ui/helpers.ts": 
  /*!************************************************!*\
  !*** ./src/world_info_optimizer/ui/helpers.ts ***!
  \************************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   escapeHtml: () => (/* binding */ escapeHtml),\n/* harmony export */   highlightText: () => (/* binding */ highlightText)\n/* harmony export */ });\n// src/world_info_optimizer/ui/helpers.ts\nconst escapeHtml = (text) => {\n    if (typeof text !== 'string')\n        text = String(text);\n    const p = document.createElement('p');\n    p.textContent = text;\n    return p.innerHTML;\n};\nconst highlightText = (text, searchTerm) => {\n    if (!searchTerm || !text)\n        return escapeHtml(text);\n    const escapedText = escapeHtml(text);\n    const htmlSafeSearchTerm = escapeHtml(searchTerm);\n    const escapedSearchTerm = htmlSafeSearchTerm.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n    const regex = new RegExp(`(${escapedSearchTerm})`, 'gi');\n    return escapedText.replace(regex, '<mark class=\"wio-highlight\">$1</mark>');\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/helpers.ts\n\n}");
  },
  "./src/world_info_optimizer/ui/modals.ts": 
  /*!***********************************************!*\
  !*** ./src/world_info_optimizer/ui/modals.ts ***!
  \***********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   showEntryEditorModal: () => (/* binding */ showEntryEditorModal),\n/* harmony export */   showModal: () => (/* binding */ showModal),\n/* harmony export */   showProgressToast: () => (/* binding */ showProgressToast),\n/* harmony export */   showRegexEditorModal: () => (/* binding */ showRegexEditorModal),\n/* harmony export */   showSuccessTick: () => (/* binding */ showSuccessTick)\n/* harmony export */ });\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./helpers */ \"./src/world_info_optimizer/ui/helpers.ts\");\n// src/world_info_optimizer/ui/modals.ts\n\nconst showModal = (options) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        return new Promise((resolve, reject) => {\n            const result = window.parent.prompt(options.text, typeof options.value === 'string' ? options.value : '');\n            if (result !== null)\n                resolve(result);\n            else\n                reject(new Error('Prompt cancelled'));\n        });\n    }\n    const type = options.type || 'alert';\n    switch (type) {\n        case 'confirm':\n            return Swal.fire({\n                title: options.title || '确认',\n                text: options.text,\n                icon: 'warning',\n                showCancelButton: true,\n                confirmButtonText: '确认',\n                cancelButtonText: '取消',\n            }).then((result) => {\n                if (result.isConfirmed) {\n                    return true;\n                }\n                throw new Error('Confirmation cancelled');\n            });\n        case 'prompt':\n            return Swal.fire({\n                title: options.title,\n                text: options.text,\n                input: 'text',\n                inputValue: options.value || '',\n                showCancelButton: true,\n                confirmButtonText: '确定',\n                cancelButtonText: '取消',\n            }).then((result) => {\n                if (result.isConfirmed && typeof result.value === 'string') {\n                    return result.value;\n                }\n                throw new Error('Prompt cancelled');\n            });\n        case 'alert':\n        default:\n            return Swal.fire({\n                title: options.title || '提示',\n                text: options.text,\n                icon: 'info',\n            }).then(() => true);\n    }\n};\nconst showSuccessTick = (message = '操作成功', duration = 1500) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        console.log(`[SUCCESS] ${message}`);\n        return;\n    }\n    const Toast = Swal.mixin({\n        toast: true,\n        position: 'top-end',\n        showConfirmButton: false,\n        timer: duration,\n        timerProgressBar: true,\n        didOpen: (toast) => {\n            toast.addEventListener('mouseenter', Swal.stopTimer);\n            toast.addEventListener('mouseleave', Swal.resumeTimer);\n        },\n    });\n    Toast.fire({\n        icon: 'success',\n        title: message,\n    });\n};\nconst showEntryEditorModal = (options) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        return Promise.reject(new Error('Swal not found!'));\n    }\n    const { entry, isCreating, bookName } = options;\n    const title = isCreating ? `在 \"${bookName}\" 中创建新条目` : `编辑条目: ${entry.comment}`;\n    return Swal.fire({\n        title: title,\n        html: `\n            <div style=\"text-align: left;\">\n                <label for=\"swal-comment\" class=\"swal2-label\">注释 (条目名)</label>\n                <input id=\"swal-comment\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(entry.comment || '')}\" aria-required=\"true\">\n\n                <label for=\"swal-keys\" class=\"swal2-label\">关键词 (逗号分隔)</label>\n                <input id=\"swal-keys\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)((entry.keys || []).join(', '))}\">\n\n                <label for=\"swal-content\" class=\"swal2-label\">内容</label>\n                <textarea id=\"swal-content\" class=\"swal2-textarea\">${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(entry.content || '')}</textarea>\n            </div>\n        `,\n        showCancelButton: true,\n        confirmButtonText: '保存',\n        cancelButtonText: '取消',\n        preConfirm: () => {\n            const comment = document.getElementById('swal-comment').value;\n            const keys = document.getElementById('swal-keys').value;\n            const content = document.getElementById('swal-content').value;\n            if (!comment && !keys) {\n                Swal.showValidationMessage('注释和关键词不能都为空');\n                return false;\n            }\n            return { comment, keys, content };\n        },\n    }).then((result) => {\n        if (result.isConfirmed) {\n            const { comment, keys, content } = result.value;\n            return {\n                ...entry,\n                comment,\n                keys: keys\n                    .split(',')\n                    .map((k) => k.trim())\n                    .filter(Boolean),\n                content,\n            };\n        }\n        throw new Error('Entry editor cancelled');\n    });\n};\nconst showProgressToast = (initialMessage = '正在处理...') => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        console.log(`[PROGRESS] ${initialMessage}`);\n        return {\n            update: (newMessage) => console.log(`[PROGRESS UPDATE] ${newMessage}`),\n            remove: () => console.log(`[PROGRESS] Done.`),\n        };\n    }\n    Swal.fire({\n        toast: true,\n        position: 'bottom-end',\n        title: initialMessage,\n        showConfirmButton: false,\n        didOpen: () => {\n            Swal.showLoading();\n        },\n    });\n    return {\n        update: (newMessage) => {\n            Swal.update({\n                title: newMessage,\n            });\n        },\n        remove: () => {\n            Swal.close();\n        },\n    };\n};\nconst showRegexEditorModal = (options) => {\n    const Swal = window.parent.Swal;\n    if (!Swal) {\n        return Promise.reject(new Error('Swal not found!'));\n    }\n    const { regex, isCreating } = options;\n    const title = isCreating ? '创建新正则' : `编辑正则: ${regex.script_name}`;\n    return Swal.fire({\n        title: title,\n        html: `\n            <div style=\"text-align: left;\">\n                <label for=\"swal-script-name\" class=\"swal2-label\">名称</label>\n                <input id=\"swal-script-name\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.script_name || '')}\" aria-required=\"true\">\n\n                <label for=\"swal-find-regex\" class=\"swal2-label\">查找 (正则表达式)</label>\n                <input id=\"swal-find-regex\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.find_regex || '')}\" aria-required=\"true\">\n\n                <label for=\"swal-replace-string\" class=\"swal2-label\">替换为</label>\n                <input id=\"swal-replace-string\" class=\"swal2-input\" value=\"${(0,_helpers__WEBPACK_IMPORTED_MODULE_0__.escapeHtml)(regex.replace_string || '')}\">\n            </div>\n        `,\n        showCancelButton: true,\n        confirmButtonText: '保存',\n        cancelButtonText: '取消',\n        preConfirm: () => {\n            const script_name = document.getElementById('swal-script-name').value;\n            const find_regex = document.getElementById('swal-find-regex').value;\n            const replace_string = document.getElementById('swal-replace-string').value;\n            if (!script_name || !find_regex) {\n                Swal.showValidationMessage('名称和查找正则不能为空');\n                return false;\n            }\n            return { script_name, find_regex, replace_string };\n        },\n    }).then((result) => {\n        if (result.isConfirmed) {\n            const { script_name, find_regex, replace_string } = result.value;\n            return {\n                ...regex,\n                script_name,\n                find_regex,\n                replace_string,\n            };\n        }\n        throw new Error('Regex editor cancelled');\n    });\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/modals.ts\n\n}");
  },
  "./src/world_info_optimizer/ui/views.ts": 
  /*!**********************************************!*\
  !*** ./src/world_info_optimizer/ui/views.ts ***!
  \**********************************************/ (__unused_webpack_module, __webpack_exports__, __webpack_require__) => {
    eval("{__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   renderCharacterLorebookView: () => (/* binding */ renderCharacterLorebookView),\n/* harmony export */   renderChatLorebookView: () => (/* binding */ renderChatLorebookView),\n/* harmony export */   renderGlobalLorebookView: () => (/* binding */ renderGlobalLorebookView),\n/* harmony export */   renderRegexView: () => (/* binding */ renderRegexView)\n/* harmony export */ });\n/* harmony import */ var _store__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../store */ \"./src/world_info_optimizer/store.ts\");\n/* harmony import */ var _elements__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./elements */ \"./src/world_info_optimizer/ui/elements.ts\");\n/* harmony import */ var _helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./helpers */ \"./src/world_info_optimizer/ui/helpers.ts\");\n// src/world_info_optimizer/ui/views.ts\n\n\n\nconst renderGlobalLorebookView = (state, searchTerm) => {\n    const books = [...state.allLorebooks].sort((a, b) => (b.enabled ? 1 : -1) - (a.enabled ? 1 : -1) || a.name.localeCompare(b.name));\n    if (books.length === 0)\n        return `<p class=\"wio-info-text\">没有找到全局世界书。</p>`;\n    if (!searchTerm) {\n        return books.map(book => (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(book, state, searchTerm)).join('');\n    }\n    const filteredBookHtml = books\n        .map(book => {\n        const bookNameMatches = book.name.toLowerCase().includes(searchTerm);\n        const entries = state.lorebookEntries.get(book.name) || [];\n        const filteredEntries = entries.filter(entry => (entry.comment || '').toLowerCase().includes(searchTerm) ||\n            (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||\n            (entry.content || '').toLowerCase().includes(searchTerm));\n        if (bookNameMatches || filteredEntries.length > 0) {\n            const entriesToShow = bookNameMatches ? entries : filteredEntries;\n            return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(book, state, searchTerm, true, entriesToShow);\n        }\n        return '';\n    })\n        .join('');\n    return filteredBookHtml || `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的结果。</p>`;\n};\nconst renderCharacterLorebookView = (state, searchTerm) => {\n    const linkedBooks = state.lorebooks.character;\n    if (linkedBooks.length === 0)\n        return `<p class=\"wio-info-text\">当前角色没有绑定的世界书。</p>`;\n    if (!searchTerm) {\n        return linkedBooks\n            .map(bookName => {\n            const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };\n            return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false);\n        })\n            .join('');\n    }\n    const filteredBookHtml = linkedBooks\n        .map(bookName => {\n        const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };\n        const bookNameMatches = bookFile.name.toLowerCase().includes(searchTerm);\n        const entries = state.lorebookEntries.get(bookFile.name) || [];\n        const filteredEntries = entries.filter(entry => (entry.comment || '').toLowerCase().includes(searchTerm) ||\n            (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||\n            (entry.content || '').toLowerCase().includes(searchTerm));\n        if (bookNameMatches || filteredEntries.length > 0) {\n            const entriesToShow = bookNameMatches ? entries : filteredEntries;\n            return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false, entriesToShow);\n        }\n        return '';\n    })\n        .join('');\n    return filteredBookHtml || `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的结果。</p>`;\n};\nconst renderChatLorebookView = (state, searchTerm) => {\n    const bookName = state.chatLorebook;\n    if (!bookName)\n        return `<p class=\"wio-info-text\">当前聊天没有绑定的世界书。</p>`;\n    const bookFile = state.allLorebooks.find(b => b.name === bookName) || { name: bookName, enabled: false };\n    if (!searchTerm) {\n        return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false);\n    }\n    const bookNameMatches = bookFile.name.toLowerCase().includes(searchTerm);\n    const entries = state.lorebookEntries.get(bookFile.name) || [];\n    const filteredEntries = entries.filter(entry => (entry.comment || '').toLowerCase().includes(searchTerm) ||\n        (entry.keys || []).join(', ').toLowerCase().includes(searchTerm) ||\n        (entry.content || '').toLowerCase().includes(searchTerm));\n    if (bookNameMatches || filteredEntries.length > 0) {\n        const entriesToShow = bookNameMatches ? entries : filteredEntries;\n        return (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createLorebookElement)(bookFile, state, searchTerm, false, entriesToShow);\n    }\n    return `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的结果。</p>`;\n};\nconst renderRegexView = (regexes, searchTerm, title, scope) => {\n    let content = `<div class=\"wio-regex-group\" data-scope=\"${scope}\"><h3>${title} (${regexes.length})</h3>`;\n    if (regexes.length === 0 && scope === 'character' && !(0,_store__WEBPACK_IMPORTED_MODULE_0__.getState)().character) {\n        content += `<p class=\"wio-info-text\">没有加载角色，无法显示角色正则。</p>`;\n    }\n    else if (regexes.length === 0) {\n        content += `<p class=\"wio-info-text\">没有找到正则。</p>`;\n    }\n    const filteredRegexes = searchTerm\n        ? regexes.filter(r => (r.script_name || '').toLowerCase().includes(searchTerm) ||\n            (r.find_regex || '').toLowerCase().includes(searchTerm) ||\n            (r.replace_string || '').toLowerCase().includes(searchTerm))\n        : regexes;\n    if (filteredRegexes.length > 0) {\n        content += filteredRegexes.map(r => (0,_elements__WEBPACK_IMPORTED_MODULE_1__.createRegexItemElement)(r, searchTerm)).join('');\n    }\n    else if (searchTerm) {\n        content += `<p class=\"wio-info-text\">没有找到与 \"${(0,_helpers__WEBPACK_IMPORTED_MODULE_2__.escapeHtml)(searchTerm)}\" 匹配的正则。</p>`;\n    }\n    if (scope === 'global' || (scope === 'character' && (0,_store__WEBPACK_IMPORTED_MODULE_0__.getState)().character)) {\n        content += `<div class=\"wio-regex-actions\"><button class=\"wio-create-regex-btn\" data-scope=\"${scope}\">+ 新建正则</button></div>`;\n    }\n    return content + '</div>';\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///./src/world_info_optimizer/ui/views.ts\n\n}");
  }
};

var __webpack_module_cache__ = {};

function __webpack_require__(moduleId) {
  var cachedModule = __webpack_module_cache__[moduleId];
  if (cachedModule !== undefined) {
    return cachedModule.exports;
  }
  var module = __webpack_module_cache__[moduleId] = {
    exports: {}
  };
  __webpack_modules__[moduleId](module, module.exports, __webpack_require__);
  return module.exports;
}

(() => {
  __webpack_require__.d = (exports, definition) => {
    for (var key in definition) {
      if (__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {
        Object.defineProperty(exports, key, {
          enumerable: true,
          get: definition[key]
        });
      }
    }
  };
})();

(() => {
  __webpack_require__.o = (obj, prop) => Object.prototype.hasOwnProperty.call(obj, prop);
})();

(() => {
  __webpack_require__.r = exports => {
    if (typeof Symbol !== "undefined" && Symbol.toStringTag) {
      Object.defineProperty(exports, Symbol.toStringTag, {
        value: "Module"
      });
    }
    Object.defineProperty(exports, "__esModule", {
      value: true
    });
  };
})();

var __webpack_exports__ = __webpack_require__("./src/world_info_optimizer/index.ts");